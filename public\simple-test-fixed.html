<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget Simple Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #333;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget Simple Test</h1>
    <p>This page tests if the QuoteAI widget loads correctly.</p>
    
    <div id="status"></div>
    
    <button id="clearStorage">Clear LocalStorage</button>
  </div>

  <!-- Load React and ReactDOM (required dependencies) -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the widget script -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
  
  <script>
    // Function to log messages to the status div
    function log(message, type = 'info') {
      const statusDiv = document.getElementById('status');
      const messageElement = document.createElement('div');
      messageElement.className = `status ${type}`;
      messageElement.textContent = message;
      statusDiv.appendChild(messageElement);
    }
    
    // Initialize the widget when the DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      try {
        log('Checking if widget script is loaded...', 'info');
        
        if (typeof window.initChatWidget === 'function') {
          log('Widget script loaded successfully!', 'success');
          
          // Initialize the widget
          window.initChatWidget({
            apiKey: ";C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
            websiteUrl: "example.com",
            apiUrl: "http://localhost:8080"
          });
          
          log('Widget initialized!', 'success');
        } else {
          log('Widget script failed to load. initChatWidget function is not available.', 'error');
        }
      } catch (error) {
        log(`Error: ${error.message}`, 'error');
      }
    });
    
    // Clear localStorage when button is clicked
    document.getElementById('clearStorage').addEventListener('click', () => {
      localStorage.clear();
      log('LocalStorage cleared', 'success');
      
      // Remove the widget container if it exists
      const widgetContainer = document.getElementById('quoteai-widget-container');
      if (widgetContainer) {
        widgetContainer.remove();
        log('Widget container removed', 'success');
      }
    });
  </script>
</body>
</html>
