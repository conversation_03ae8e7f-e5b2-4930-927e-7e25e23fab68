<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget Integration Example</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #3498db;
      padding-bottom: 10px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    pre {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      overflow-x: auto;
    }
    code {
      font-family: Consolas, Monaco, 'Andale Mono', monospace;
    }
    .note {
      background-color: #fff8dc;
      border-left: 4px solid #ffd700;
      padding: 10px 15px;
      margin: 20px 0;
    }
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px 0;
    }
    button:hover {
      background-color: #2980b9;
    }
    .config-panel {
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin: 20px 0;
    }
    .config-panel input {
      width: 100%;
      padding: 8px;
      margin: 5px 0 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .config-panel label {
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget Integration Example</h1>

    <p>This page demonstrates how to integrate the QuoteAI chatbot widget with the new combined domain verification and API key authentication.</p>

    <div class="config-panel">
      <h2>Widget Configuration</h2>
      <form id="widget-config">
        <div>
          <label for="api-key">API Key:</label>
          <input type="text" id="api-key" value=";C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL">
        </div>
        <div>
          <label for="website-url">Website URL:</label>
          <input type="text" id="website-url" value="example.com">
        </div>
        <div>
          <label for="api-url">API URL:</label>
          <input type="text" id="api-url" value="http://localhost:8080">
        </div>
        <button type="button" id="initialize-widget">Initialize Widget</button>
        <button type="button" id="reset-widget">Reset Widget</button>
      </form>
    </div>

    <h2>Integration Code</h2>
    <p>Here's how to integrate the QuoteAI widget into your website:</p>

    <pre><code>&lt;!-- Include React and ReactDOM (required dependencies) --&gt;
&lt;script src="https://unpkg.com/react@18.0.0/umd/react.development.js"&gt;&lt;/script&gt;
&lt;script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"&gt;&lt;/script&gt;

&lt;!-- Include the QuoteAI widget script --&gt;
&lt;script src="https://widget.quoteai.com/chatbot-widget.umd.js"&gt;&lt;/script&gt;
&lt;link rel="stylesheet" href="https://widget.quoteai.com/style.css"&gt;

&lt;script&gt;
  document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.initChatWidget === 'function') {
      window.initChatWidget({
        apiKey: "YOUR_API_KEY",
        websiteUrl: "your-domain.com", // Your website domain
        apiUrl: "https://api.quoteai.com" // Optional: API URL override
      });
    }
  });
&lt;/script&gt;</code></pre>

    <div class="note">
      <strong>Note:</strong> Replace <code>YOUR_API_KEY</code> with the API key provided by QuoteAI. The <code>websiteUrl</code> should match the domain registered with your QuoteAI account.
    </div>

    <h2>Authentication Parameters</h2>
    <p>The widget now uses a combined authentication approach:</p>
    <ul>
      <li><strong>apiKey</strong>: Your unique API key for authentication</li>
      <li><strong>websiteUrl</strong>: Your website domain for domain verification</li>
    </ul>

    <p>This combined approach provides better security and allows for multiple clients with their own users.</p>
  </div>

  <!-- Load React and ReactDOM from CDN (required dependencies) -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

  <!-- Load the widget script -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const initButton = document.getElementById('initialize-widget');
      const resetButton = document.getElementById('reset-widget');

      initButton.addEventListener('click', () => {
        // Get configuration values
        const apiKey = document.getElementById('api-key').value;
        const websiteUrl = document.getElementById('website-url').value;
        const apiUrl = document.getElementById('api-url').value;

        // Remove existing widget if any
        const existingWidget = document.getElementById('quoteai-widget-container');
        if (existingWidget) {
          existingWidget.remove();
        }

        // Initialize widget with new configuration
        if (typeof window.initChatWidget === 'function') {
          window.initChatWidget({
            apiKey: apiKey,
            websiteUrl: websiteUrl,
            apiUrl: apiUrl
          });
          console.log('Widget initialized with:', { apiKey, websiteUrl, apiUrl });
        } else {
          console.error('initChatWidget function not available');
          alert('Widget script not loaded properly. Please check the console for errors.');
        }
      });

      resetButton.addEventListener('click', () => {
        // Clear localStorage
        localStorage.clear();

        // Remove existing widget
        const existingWidget = document.getElementById('quoteai-widget-container');
        if (existingWidget) {
          existingWidget.remove();
        }

        console.log('Widget reset. LocalStorage cleared.');
        alert('Widget reset. You can initialize it again with new settings.');
      });
    });
  </script>
</body>
</html>
