from docx import Document
from PyPDF2 import PdfReader
import json


# For begineers, we will do a simple parsing. Nasty Parsing where we have no formatting. This is just to get things started.
# The knowledge base doc needs to be stored in the database under uuid -> knowledge-base-doc
# JSON is for creating assistants only.


def extract_text_from_pdf(file_path):
    reader = PdfReader(file_path)
    text = ""
    for page in reader.pages:
        text += page.extract_text() + "\n"
    return text

# Example usage
pdf_path = "/Users/<USER>/Desktop/QuoteAI/code/quoteai/server/lib/knowledge_base/Ikrams_Plumbing_KB.pdf"
text = extract_text_from_pdf(pdf_path)
print(text)