/**
 * Step Navigation Component
 * Displays progress indicators and allows navigation between steps
 */

import React from 'react';
import { motion } from 'framer-motion';

interface Step {
  id: number;
  title: string;
  icon: string;
}

interface StepNavigationProps {
  steps: Step[];
  currentStep: number;
  completedSteps: number[];
  onStepClick: (stepId: number) => void;
}

const StepNavigation: React.FC<StepNavigationProps> = ({
  steps,
  currentStep,
  completedSteps,
  onStepClick
}) => {
  const getStepStatus = (stepId: number) => {
    if (completedSteps.includes(stepId)) return 'completed';
    if (stepId === currentStep) return 'current';
    return 'pending';
  };

  const isClickable = (stepId: number) => {
    return completedSteps.includes(stepId) || stepId === currentStep;
  };

  return (
    <div className="step-navigation">
      <div className="step-progress-bar">
        <div className="step-list">
          {steps.map((step, index) => {
            const status = getStepStatus(step.id);
            const clickable = isClickable(step.id);
            
            return (
              <div key={step.id} className="step-item">
                {/* Connection Line */}
                {index > 0 && (
                  <div className="step-connector">
                    <motion.div
                      className="step-line"
                      initial={{ scaleX: 0 }}
                      animate={{ 
                        scaleX: completedSteps.includes(steps[index - 1].id) ? 1 : 0 
                      }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                )}

                {/* Step Circle */}
                <motion.div
                  className={`step-circle ${status} ${clickable ? 'clickable' : ''}`}
                  onClick={() => clickable && onStepClick(step.id)}
                  whileHover={clickable ? { scale: 1.05 } : {}}
                  whileTap={clickable ? { scale: 0.95 } : {}}
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {status === 'completed' ? (
                    <motion.svg
                      className="step-check"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="3"
                      initial={{ pathLength: 0 }}
                      animate={{ pathLength: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <path d="M20 6L9 17l-5-5" />
                    </motion.svg>
                  ) : status === 'current' ? (
                    <span className="step-icon">{step.icon}</span>
                  ) : (
                    <span className="step-number">{step.id}</span>
                  )}
                </motion.div>

                {/* Step Info */}
                <div className="step-info">
                  <h3 className="step-title">{step.title}</h3>
                </div>
              </div>
            );
          })}
        </div>

        {/* Progress Bar */}
        <div className="progress-bar-container">
          <div className="progress-bar-background" />
          <motion.div
            className="progress-bar-fill"
            initial={{ width: '0%' }}
            animate={{ 
              width: `${(completedSteps.length / steps.length) * 100}%` 
            }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
          />
        </div>
      </div>

      {/* Mobile Step Indicator */}
      <div className="mobile-step-indicator">
        <div className="mobile-progress">
          <span className="current-step">Step {currentStep}</span>
          <span className="total-steps">of {steps.length}</span>
        </div>
        <div className="mobile-progress-bar">
          <motion.div
            className="mobile-progress-fill"
            initial={{ width: '0%' }}
            animate={{ 
              width: `${(currentStep / steps.length) * 100}%` 
            }}
            transition={{ duration: 0.3 }}
          />
        </div>
        <h3 className="mobile-step-title">{steps[currentStep - 1]?.title}</h3>
      </div>
    </div>
  );
};

export default StepNavigation;
