# QuoteAI Design Documentation

## UI Components Visual Guide

This document provides a visual reference for the QuoteAI chat widget UI components and their styling.

## Widget States

### Closed State (Initial)
When the widget is in its closed state, only the floating button is visible in the bottom-right corner of the screen.

```
+------------------+
|                  |
|                  |
|                  |
|                  |
|                  |
|                  |
|                  |
|         +--------+
|         | 💬 Get |
|         | Quote! |
+---------+--------+
```

**Styling:**
```css
.chat-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 12px;
  border: none;
  border-radius: 14px;
  background-color: #1b8ae4;
  color: white;
  font-size: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
```

### Open State
When the button is clicked, the full chat window appears above the button.

```
+------------------+
|                  |
| +---------------+|
| |[Logo] Chat    ×||
| |---------------||
| |               ||
| | Bot: G'day    ||
| | Mate, how can ||
| | I help?       ||
| |               ||
| |               ||
| |               ||
| |---------------||
| | [Input field] ||
| | 📎 😊        ||
| +---------------+|
|         +--------+
|         | 💬 Get |
|         | Quote! |
+---------+--------+
```

**Styling:**
```css
.chat-container {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 350px;
  height: 560px;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  background-color: white;
}

.chat-container.open {
  transform: translateY(0);
  opacity: 1;
}
```

## Component Breakdown

### 1. Chat Button

The floating button that initiates the chat.

**Visual:**
```
+---------------+
| 💬 Get a Free |
|    Quote!     |
+---------------+
```

**Behavior:**
- Hovers with a slight scale effect
- Transitions to secondary color on hover
- Clicking toggles the chat window

### 2. Chat Header

The top bar of the chat window.

**Visual:**
```
+--------------------------------+
| [Logo] Chat with us!         × |
+--------------------------------+
```

**Styling:**
```css
.chat-header {
  padding: 16px;
  background-color: #1b8ae4;
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
```

### 3. Message Bubbles

The chat messages displayed in the conversation area.

**Bot Message:**
```
+---------------------------+
| G'day Mate, how can I     |
| help?                     |
+---------------------------+
```

**User Message:**
```
                +-----------+
                | Hello, I  |
                | need help |
                +-----------+
```

**Styling:**
```css
.message {
  max-width: 80%;
  padding: 12px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.3;
}

.message.user {
  background-color: #1b8ae4;
  color: white;
  align-self: flex-end;
  border-bottom-right-radius: 2px;
}

.message.bot {
  background-color: #f1f1f1;
  color: #333;
  align-self: flex-start;
  border-bottom-left-radius: 2px;
}
```

### 4. Input Area

The bottom section where users type their messages.

**Visual:**
```
+--------------------------------+
| Enter your message...          |
| 📎 😊                         |
+--------------------------------+
```

**Styling:**
```css
.chat-input {
  padding: 12px;
  border-top: 1px solid #eee;
  background-color: white;
}

.chat-input input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 18px;
  font-size: 14px;
}
```

### 5. Loading Animation

Displayed when waiting for the bot to respond.

**Visual:**
```
+---------------------------+
| .                         |
|  .                        |
|   .                       |
+---------------------------+
```

**Styling:**
```css
.loading-dots {
  display: flex;
  padding: 10px;
  align-self: flex-start;
}

.loading-dots span {
  animation: loadingDots 1.4s infinite ease-in-out;
  background-color: #ccc;
  border-radius: 50%;
  display: inline-block;
  height: 8px;
  width: 8px;
  margin-right: 4px;
}
```

## Color Palette

### Primary Colors

| Color Name | Hex Code | Usage |
|------------|----------|-------|
| Primary Blue | #1b8ae4 | Chat button, header, user messages |
| Secondary Blue | #53a2be | Hover states |
| Background | #ffffff | Chat container background |
| Light Gray | #f1f1f1 | Bot message bubbles |
| Dark Gray | #333333 | Text color |

### UI States

| State | Element | Color Change |
|-------|---------|-------------|
| Default | Chat Button | #1b8ae4 |
| Hover | Chat Button | #53a2be |
| Default | User Message | #1b8ae4 |
| Default | Bot Message | #f1f1f1 |
| Default | Input Border | #dddddd |
| Focus | Input Border | #1b8ae4 |

## Typography

| Element | Font Family | Weight | Size | Color |
|---------|-------------|--------|------|-------|
| Chat Header | Montserrat | 600 | 18px | #ffffff |
| Message Text | Open Sans | 400 | 14px | Varies |
| Input Text | Open Sans | 400 | 14px | #333333 |
| Button Text | Open Sans | 400 | 20px | #ffffff |

## Animations

### Fade In
Used when new messages appear:
```css
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
```

### Loading Dots
Used when waiting for bot response:
```css
@keyframes loadingDots {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}
```

### Chat Open/Close
Used when toggling the chat window:
```css
.chat-container {
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.chat-container.open {
  transform: translateY(0);
  opacity: 1;
}
```

## Responsive Behavior

### Desktop (>768px)
- Chat window: 350px width, 560px height
- Positioned in bottom-right corner
- Full functionality with all UI elements

### Mobile (<768px)
- Chat window: 90% width, 70% viewport height
- Centered at bottom of screen
- Adjusted padding and font sizes for better touch targets
- Simplified UI with focus on essential elements

## Accessibility Considerations

- Color contrast meets WCAG AA standards
- Interactive elements have appropriate hover/focus states
- Chat can be navigated using keyboard (tab navigation)
- Input field supports Enter key for sending messages
- Close button is clearly visible and accessible
