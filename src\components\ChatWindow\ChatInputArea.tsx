import React, { useState, ChangeEvent, KeyboardEvent, useRef, useEffect } from 'react';

interface ChatInputAreaProps {
  onSendMessage: (text: string) => void;
  onAttachClick: () => void;
  onCameraClick: () => void;
  isSending: boolean; // For disabling input/buttons
  hasSelectedImages: boolean; // To enable send button even if text is empty
  isPortalMode?: boolean;
}

const ChatInputArea: React.FC<ChatInputAreaProps> = ({
  onSendMessage,
  onAttachClick,
  onCameraClick,
  isSending,
  hasSelectedImages,
  isPortalMode = false,
}) => {
  const [inputValue, setInputValue] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-resize textarea function
  const autoResizeTextarea = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  };

  // Handle input change for both input and textarea
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    if (isPortalMode && textareaRef.current) {
      autoResizeTextarea();
    }
  };

  const handleSend = () => {
    const trimmedInput = inputValue.trim();
    // Allow sending if we have either text content or selected images
    if (trimmedInput === '' && !hasSelectedImages) return;
    onSendMessage(trimmedInput);
    setInputValue('');

    // Reset textarea height after sending
    if (isPortalMode && textareaRef.current) {
      textareaRef.current.style.height = 'auto';
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  // Auto-resize on mount and when value changes
  useEffect(() => {
    if (isPortalMode && textareaRef.current) {
      autoResizeTextarea();
    }
  }, [inputValue, isPortalMode]);

  return (
    <div className={`chat-input ${isPortalMode ? 'portal-mode' : ''}`}>
      {isPortalMode ? (
        // Portal mode: horizontal layout with auto-expanding textarea
        <div className="input-row">
          <textarea
            ref={textareaRef}
            value={inputValue}
            placeholder={hasSelectedImages ? "Add a message (optional)..." : "Type your message here..."}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={isSending}
            aria-label="Chat message input"
            className="portal-textarea"
            rows={1}
            style={{ resize: 'none', overflow: 'hidden' }}
          />
          <div className="portal-buttons">
            <button
              className="icon-button attach-button"
              data-tooltip="Attach Photo"
              aria-label="Attach photo from files"
              onClick={onAttachClick}
              disabled={isSending}
            >
              📎
            </button>
            <button
              className="icon-button camera-button"
              data-tooltip="Take Photo"
              aria-label="Take photo with camera"
              onClick={onCameraClick}
              disabled={isSending}
            >
              📷
            </button>
            <button
              className="icon-button send-button"
              data-tooltip="Send Message"
              aria-label="Send message"
              onClick={handleSend}
              disabled={isSending || (inputValue.trim() === '' && !hasSelectedImages)}
            >
              ➤
            </button>
          </div>
        </div>
      ) : (
        // Regular mode: vertical layout (existing behavior)
        <>
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            placeholder={hasSelectedImages ? "Add a message (optional)..." : "Enter your message..."}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            disabled={isSending}
            aria-label="Chat message input"
          />
          <div className="icons">
            <button
              className="icon-button"
              data-tooltip="Attach Photo"
              aria-label="Attach photo from files"
              onClick={onAttachClick}
              disabled={isSending}
            >
              📎
            </button>
            <button
              className="icon-button"
              data-tooltip="Take Photo"
              aria-label="Take photo with camera"
              onClick={onCameraClick}
              disabled={isSending}
            >
              📷
            </button>
            <button
              className="icon-button send-button"
              data-tooltip="Send Message"
              aria-label="Send message"
              onClick={handleSend}
              disabled={isSending || (inputValue.trim() === '' && !hasSelectedImages)}
            >
              ➤
            </button>
          </div>
        </>
      )}
    </div>
  );
};

export default ChatInputArea;