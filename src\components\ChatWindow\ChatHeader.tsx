import React from 'react';

interface ChatHeaderProps {
  onClose: () => void;
  onClearSession: () => void;
  chatIconUrl?: string;
  isPortalMode?: boolean;
  tradieName?: string;
}

// Utility function to format tradie name for display
const formatTradieName = (tradieName: string): string => {
  if (!tradieName || tradieName.trim() === '') {
    return 'Unknown';
  }

  return tradieName
    .trim()
    .split(/[-_\s]+/) // Split on hyphens, underscores, and spaces
    .filter(word => word.length > 0) // Remove empty strings
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize each word
    .join(' '); // Join with spaces
};

const ChatHeader: React.FC<ChatHeaderProps> = ({
  onClose,
  onClearSession,
  chatIconUrl = "/quoteai.png",
  isPortalMode = false,
  tradieName
}) => {
  // Generate the header text based on mode and tradie name
  const getHeaderText = (): string => {
    if (isPortalMode && tradieName) {
      const formattedName = formatTradieName(tradieName);
      return `Chat with ${formattedName}`;
    }
    return isPortalMode ? 'Chat Support' : 'Chat with us!';
  };

  return (
    <div className={`chat-header ${isPortalMode ? 'portal-mode' : ''}`}>
      <img src={chatIconUrl} alt="Chatbot Icon" className="chat-icon" />
      {getHeaderText()}
      <div className="header-buttons">
        <button
          className="clear-button"
          onClick={onClearSession}
          title="Clear chat history and start new session"
          aria-label="Clear chat history"
        >
          🗑️
        </button>
        <button
          className="close-button"
          onClick={onClose}
          title={isPortalMode ? "Go back" : "Close chat"}
          aria-label={isPortalMode ? "Go back" : "Close chat"}
        >
          {isPortalMode ? '←' : '×'}
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;