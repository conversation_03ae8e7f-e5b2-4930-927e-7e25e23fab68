import { useState, useCallback, ChangeEvent } from 'react';
import { uploadImageApi } from '../services/apiService';
import { compressImage } from '../utils/imageCompressor';
import { MAX_IMAGES_UPLOAD, MAX_IMAGE_SIZE_BYTES, ERROR_MESSAGES } from '../constants';
import type { ImagePreview as ImagePreviewType } from '../types'; // Renamed to avoid conflict

export interface UseImageHandlerReturn {
  selectedImages: File[];
  previewImageObjects: ImagePreviewType[]; // Use the renamed type
  isUploading: boolean;
  imageError: string | null;
  handleFileSelect: (event: ChangeEvent<HTMLInputElement>) => Promise<void>;
  removeImage: (index: number) => void;
  clearAllImages: () => void;
  uploadSelectedImages: (apiKey: string, threadId: string, customApiUrl?: string) => Promise<string[]>;
  triggerFileInput: (inputRef: React.RefObject<HTMLInputElement>, capture?: 'user' | 'environment') => void;
}

export const useImageHandler = (): UseImageHandlerReturn => {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [previewImageObjects, setPreviewImageObjects] = useState<ImagePreviewType[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [imageError, setImageError] = useState<string | null>(null);

  const addImage = useCallback(async (file: File) => {
    setImageError(null);
    if (selectedImages.length >= MAX_IMAGES_UPLOAD) {
      setImageError(`You can select a maximum of ${MAX_IMAGES_UPLOAD} images.`);
      return;
    }
    if (!file.type.startsWith('image/')) {
      setImageError(ERROR_MESSAGES.FILE_TYPE_INVALID);
      return;
    }

    try {
      let processedFile = file;
      if (file.size > MAX_IMAGE_SIZE_BYTES) {
         setImageError(`Compressing large image (${(file.size / (1024*1024)).toFixed(1)}MB)...`);
         processedFile = await compressImage(file, MAX_IMAGE_SIZE_BYTES);
         setImageError(null); // Clear compression message
      }
      if (processedFile.size > MAX_IMAGE_SIZE_BYTES) {
        setImageError(ERROR_MESSAGES.FILE_TOO_LARGE);
        return;
      }

      const imageUrl = URL.createObjectURL(processedFile);
      setSelectedImages(prev => [...prev, processedFile]);
      setPreviewImageObjects(prev => [...prev, { url: imageUrl, file: processedFile }]);
    } catch (err: any) {
      console.error('Error processing image:', err);
      setImageError(err.message || 'Failed to process image.');
    }
  }, [selectedImages.length]);

  const handleFileSelect = useCallback(async (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    for (let i = 0; i < files.length; i++) {
      if (selectedImages.length + i < MAX_IMAGES_UPLOAD) {
        await addImage(files[i]);
      } else {
        setImageError(`Reached max limit of ${MAX_IMAGES_UPLOAD} images.`);
        break;
      }
    }
    // Reset file input value to allow selecting the same file again
    if (event.target) event.target.value = '';
  }, [addImage, selectedImages.length]);

  const removeImage = useCallback((index: number) => {
    const objectUrlToRemove = previewImageObjects[index]?.url;
    if (objectUrlToRemove) {
        URL.revokeObjectURL(objectUrlToRemove);
    }
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setPreviewImageObjects(prev => prev.filter((_, i) => i !== index));
    if (imageError && selectedImages.length -1 < MAX_IMAGES_UPLOAD) setImageError(null);
  }, [previewImageObjects, imageError, selectedImages.length]);

  const clearAllImages = useCallback(() => {
    previewImageObjects.forEach(p => URL.revokeObjectURL(p.url));
    setSelectedImages([]);
    setPreviewImageObjects([]);
    setImageError(null);
  }, [previewImageObjects]);

  const uploadSelectedImages = useCallback(async (
    apiKey: string,
    threadId: string,
    customApiUrl?: string
  ): Promise<string[]> => {
    if (selectedImages.length === 0) return [];
    
    setIsUploading(true);
    setImageError(null);
    const uploadedUrls: string[] = [];

    try {
      for (const imageFile of selectedImages) {
        // Re-check threadId just before upload, in case it was null during selection
        if (!threadId) throw new Error("Thread ID is not available for image upload.");
        const response = await uploadImageApi(apiKey, threadId, imageFile, customApiUrl);
        if (response.success && response.file_url) {
          uploadedUrls.push(response.file_url);
        } else {
          throw new Error(`Failed to upload ${imageFile.name}`);
        }
      }
      // Clear images after successful upload
      // clearAllImages(); // Decide if you want to clear them automatically
      return uploadedUrls;
    } catch (err: any) {
      console.error('Error uploading images:', err);
      setImageError(err.message || ERROR_MESSAGES.UPLOAD_FAILED);
      return []; // Return empty or partially uploaded URLs if preferred
    } finally {
      setIsUploading(false);
    }
  }, [selectedImages]);

  const triggerFileInput = useCallback((inputRef: React.RefObject<HTMLInputElement>, capture?: 'user' | 'environment') => {
    if (inputRef.current) {
        inputRef.current.accept = 'image/*';
        if (capture) {
            inputRef.current.capture = capture;
        } else {
            inputRef.current.removeAttribute('capture');
        }
        inputRef.current.click();
    }
  }, []);


  return {
    selectedImages,
    previewImageObjects,
    isUploading,
    imageError,
    handleFileSelect,
    removeImage,
    clearAllImages,
    uploadSelectedImages,
    triggerFileInput,
  };
};