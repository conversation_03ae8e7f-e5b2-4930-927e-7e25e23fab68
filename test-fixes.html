<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget - Test Fixes</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-weight: bold;
    }
    .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
    
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    
    button:hover {
      background-color: #0056b3;
    }
    
    #console-log {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget - Test Fixes</h1>
    
    <div class="test-section">
      <h3>Fix Status</h3>
      <div id="fix-status">
        <div class="info">Testing fixes...</div>
      </div>
    </div>
    
    <div class="test-section">
      <h3>Widget Tests</h3>
      <button onclick="testApiConnection()">Test API Connection</button>
      <button onclick="testWidgetInitialization()">Test Widget Initialization</button>
      <button onclick="clearConsole()">Clear Console</button>
    </div>
    
    <div class="test-section">
      <h3>Console Output</h3>
      <div id="console-log"></div>
    </div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the ChatWidget component -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    // Capture console logs
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalInfo = console.info;
    
    function logToPage(message, type = 'log') {
      const logDiv = document.getElementById('console-log');
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = document.createElement('div');
      logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : type === 'info' ? 'blue' : 'black';
      logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
      logDiv.appendChild(logEntry);
      logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    console.log = function(...args) {
      originalLog.apply(console, args);
      logToPage(args.join(' '), 'log');
    };
    
    console.error = function(...args) {
      originalError.apply(console, args);
      logToPage(args.join(' '), 'error');
    };
    
    console.warn = function(...args) {
      originalWarn.apply(console, args);
      logToPage(args.join(' '), 'warn');
    };
    
    console.info = function(...args) {
      originalInfo.apply(console, args);
      logToPage(args.join(' '), 'info');
    };
    
    function clearConsole() {
      document.getElementById('console-log').innerHTML = '';
    }
    
    async function testApiConnection() {
      console.info('Testing API connection to mock server...');
      
      try {
        const response = await fetch('http://localhost:8080/initiate_conversation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': ';C\\}/{ap,76L\'*m7U2!{K|21?n[*4i`;QuL',
          },
          body: JSON.stringify({})
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('API connection successful:', data);
          updateFixStatus('API Connection', 'success', 'Mock server is responding correctly');
        } else {
          console.error('API connection failed:', response.status, response.statusText);
          updateFixStatus('API Connection', 'error', `Server returned ${response.status}`);
        }
      } catch (error) {
        console.error('API connection error:', error.message);
        updateFixStatus('API Connection', 'error', error.message);
      }
    }
    
    function testWidgetInitialization() {
      console.info('Testing widget initialization...');
      
      if (typeof window.initChatWidget === 'function') {
        console.log('initChatWidget function is available');
        updateFixStatus('Widget Function', 'success', 'initChatWidget function is available');
        
        try {
          window.initChatWidget({
            apiKey: ";C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
            websiteUrl: "example.com",
            apiUrl: "http://localhost:8080"
          });
          console.log('Widget initialization called successfully');
          updateFixStatus('Widget Initialization', 'success', 'Widget initialized without errors');
        } catch (error) {
          console.error('Widget initialization error:', error);
          updateFixStatus('Widget Initialization', 'error', error.message);
        }
      } else {
        console.error('initChatWidget function is not available');
        updateFixStatus('Widget Function', 'error', 'initChatWidget function not found');
      }
    }
    
    function updateFixStatus(test, status, message) {
      const statusDiv = document.getElementById('fix-status');
      const existingStatus = statusDiv.querySelector(`[data-test="${test}"]`);
      
      if (existingStatus) {
        existingStatus.remove();
      }
      
      const statusElement = document.createElement('div');
      statusElement.className = status;
      statusElement.setAttribute('data-test', test);
      statusElement.textContent = `${test}: ${message}`;
      statusDiv.appendChild(statusElement);
    }
    
    // Auto-run tests when page loads
    document.addEventListener('DOMContentLoaded', () => {
      console.info('Page loaded, running automatic tests...');
      
      setTimeout(() => {
        testApiConnection();
      }, 1000);
      
      setTimeout(() => {
        testWidgetInitialization();
      }, 2000);
    });
  </script>
</body>
</html>
