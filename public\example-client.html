<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="<PERSON>'s Plumbing - Expert plumbing services in Inner West Sydney. Fast, reliable, and professional plumbing solutions.">
  <title><PERSON>'s Plumbing | Inner West Sydney Plumbing Services</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600&family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="styles.css">
</head>

<!-- Load React and ReactDOM from CDN (since we externalized them) -->
<!-- Load React and ReactDOM from UMD (global) -->
<script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
<script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

<!-- Load chatbot widget (local built version) -->
<script src="/dist/chatbot-widget.umd.js"></script>
<link rel="stylesheet" href="/dist/style.css">

<!-- Additional styles to ensure the chat button is fixed at bottom right -->
<style>
  /* Force the chat button to be fixed at bottom right */
  .chat-button {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    left: auto !important;
  }
</style>

<!-- Initialize widget -->
<script>
  document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.initChatWidget === 'function') {
      window.initChatWidget({
        apiKey: ";C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
        websiteUrl: "example.com",
        apiUrl: "http://localhost:8080"
      });
      console.log('Widget initialized successfully!');
    } else {
      console.error('Widget initialization failed: initChatWidget function not available');
    }
  });
</script>

<body>
  <!-- Debug Panel -->
  <div style="position: fixed; bottom: 10px; left: 10px; z-index: 1000; background: rgba(0,0,0,0.8); color: #0f0; padding: 10px; border-radius: 5px; font-family: monospace; max-width: 400px; max-height: 200px; overflow: auto;">
    <div id="debugInfo"></div>
    <button onclick="localStorage.clear(); document.getElementById('debugInfo').innerHTML = 'localStorage cleared'; setTimeout(() => window.location.reload(), 500);" style="background: #f44336; color: white; border: none; padding: 5px; margin-top: 5px; cursor: pointer;">Clear Storage</button>
  </div>

  <script>
    // Display localStorage info
    function updateDebugInfo() {
      const debugInfo = document.getElementById('debugInfo');
      const items = { ...localStorage };

      let html = '<strong>localStorage:</strong><br>';

      if (Object.keys(items).length === 0) {
        html += 'Empty<br>';
      } else {
        for (const key in items) {
          if (key.startsWith('quoteai_')) {
            try {
              const value = JSON.parse(items[key]);
              const messageCount = value.messages ? value.messages.length : 0;
              html += `${key}: ${messageCount} msgs<br>`;
            } catch (e) {
              html += `${key}: ${items[key].substring(0, 20)}...<br>`;
            }
          }
        }
      }

      html += `<br><strong>initChatWidget:</strong> ${typeof window.initChatWidget === 'function' ? '✅' : '❌'}`;

      debugInfo.innerHTML = html;
    }

    // Update debug info every second
    setInterval(updateDebugInfo, 1000);
  </script>
  <header>
    <div class="container">
      <div class="logo">
        <h1>Syed's Plumbing</h1>
        <p>Trusted Plumbers in Inner West Sydney</p>
      </div>
      <nav>
        <ul>
          <li><a href="#services">Services</a></li>
          <li><a href="#about">About</a></li>
          <li><a href="#contact">Contact</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <section id="hero">
    <div class="hero-content">
      <h2>Your Trusted Plumbers in Inner West Sydney</h2>
      <p>Reliable, fast, and professional plumbing services for homes and businesses.</p>
      <a href="#contact" class="cta-button">Get a Quote</a>
    </div>
  </section>

  <section id="services">
    <div class="container">
      <h2>Our Services</h2>
      <div class="service-list">
        <div class="service-item">
          <h3>Emergency Plumbing</h3>
          <p>24/7 emergency plumbing services. Call us anytime for immediate assistance.</p>
        </div>
        <div class="service-item">
          <h3>Drain Cleaning</h3>
          <p>Effective drain cleaning services to resolve blockages and restore flow.</p>
        </div>
        <div class="service-item">
          <h3>Leak Detection</h3>
          <p>Expert leak detection to find and fix leaks fast, preventing further damage.</p>
        </div>
        <div class="service-item">
          <h3>Pipe Replacement</h3>
          <p>Professional pipe replacement for old or damaged pipes, ensuring long-lasting performance.</p>
        </div>
      </div>
    </div>
  </section>

  <section id="about">
    <div class="container">
      <h2>About Syed's Plumbing</h2>
      <p>At Syed's Plumbing, we pride ourselves on delivering top-quality plumbing services to our local community in Inner West Sydney. With years of experience, our team is dedicated to offering fast, reliable, and affordable solutions for all your plumbing needs.</p>
    </div>
  </section>

  <section id="contact">
    <div class="container">
      <h2>Contact Us</h2>
      <form action="#" method="POST">
        <div class="form-group">
          <label for="name">Name:</label>
          <input type="text" id="name" name="name" required>
        </div>
        <div class="form-group">
          <label for="email">Email:</label>
          <input type="email" id="email" name="email" required>
        </div>
        <div class="form-group">
          <label for="message">Message:</label>
          <textarea id="message" name="message" rows="4" required></textarea>
        </div>
        <button type="submit" class="cta-button">Send Message</button>
      </form>
    </div>
  </section>

  <footer>
    <div class="container">
      <p>&copy; 2025 Syed's Plumbing | All Rights Reserved</p>
    </div>
  </footer>
</body>

</html>
