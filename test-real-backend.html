<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget - Real Backend Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-weight: bold;
    }
    .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    
    button {
      background-color: #007bff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
    }
    
    button:hover {
      background-color: #0056b3;
    }
    
    #console-log {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      max-height: 300px;
      overflow-y: auto;
    }
    
    .config-section {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 4px;
      margin: 10px 0;
    }
    
    input[type="text"] {
      width: 100%;
      padding: 8px;
      margin: 5px 0;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget - Real Backend Test</h1>
    
    <div class="config-section">
      <h3>Configuration</h3>
      <label>API Key:</label>
      <input type="text" id="apiKey" value=";C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL">
      
      <label>Website URL:</label>
      <input type="text" id="websiteUrl" value="example.com">
      
      <label>API URL:</label>
      <input type="text" id="apiUrl" value="http://localhost:8080">
    </div>
    
    <div class="test-section">
      <h3>Backend Tests</h3>
      <button onclick="testHealthCheck()">Test Health Check</button>
      <button onclick="testInitiateConversation()">Test Initiate Conversation</button>
      <button onclick="initializeWidget()">Initialize Widget</button>
      <button onclick="clearConsole()">Clear Console</button>
    </div>
    
    <div class="test-section">
      <h3>Console Output</h3>
      <div id="console-log"></div>
    </div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the ChatWidget component -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    // Capture console logs
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalInfo = console.info;
    
    function logToPage(message, type = 'log') {
      const logDiv = document.getElementById('console-log');
      const timestamp = new Date().toLocaleTimeString();
      const logEntry = document.createElement('div');
      logEntry.style.color = type === 'error' ? 'red' : type === 'warn' ? 'orange' : type === 'info' ? 'blue' : 'black';
      logEntry.textContent = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
      logDiv.appendChild(logEntry);
      logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    console.log = function(...args) {
      originalLog.apply(console, args);
      logToPage(args.join(' '), 'log');
    };
    
    console.error = function(...args) {
      originalError.apply(console, args);
      logToPage(args.join(' '), 'error');
    };
    
    console.warn = function(...args) {
      originalWarn.apply(console, args);
      logToPage(args.join(' '), 'warn');
    };
    
    console.info = function(...args) {
      originalInfo.apply(console, args);
      logToPage(args.join(' '), 'info');
    };
    
    function clearConsole() {
      document.getElementById('console-log').innerHTML = '';
    }
    
    async function testHealthCheck() {
      const apiUrl = document.getElementById('apiUrl').value;
      console.info('Testing health check endpoint...');
      
      try {
        const response = await fetch(`${apiUrl}/v1/health`);
        const data = await response.json();
        
        if (response.ok) {
          console.log('Health check successful:', data);
        } else {
          console.error('Health check failed:', response.status, data);
        }
      } catch (error) {
        console.error('Health check error:', error.message);
      }
    }
    
    async function testInitiateConversation() {
      const apiKey = document.getElementById('apiKey').value;
      const websiteUrl = document.getElementById('websiteUrl').value;
      const apiUrl = document.getElementById('apiUrl').value;
      
      console.info('Testing initiate conversation with real backend...');
      console.info(`Request body: {"website_url": "${websiteUrl}"}`);
      
      try {
        const response = await fetch(`${apiUrl}/initiate_conversation`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Api-Key': apiKey,
          },
          body: JSON.stringify({
            website_url: websiteUrl
          })
        });
        
        const data = await response.json();
        
        if (response.ok) {
          console.log('Initiate conversation successful:', data);
        } else {
          console.error('Initiate conversation failed:', response.status, data);
        }
      } catch (error) {
        console.error('Initiate conversation error:', error.message);
      }
    }
    
    function initializeWidget() {
      const apiKey = document.getElementById('apiKey').value;
      const websiteUrl = document.getElementById('websiteUrl').value;
      const apiUrl = document.getElementById('apiUrl').value;
      
      console.info('Initializing widget with real backend...');
      
      if (typeof window.initChatWidget === 'function') {
        window.initChatWidget({
          apiKey: apiKey,
          websiteUrl: websiteUrl,
          apiUrl: apiUrl
        });
        console.log('Widget initialization called successfully');
      } else {
        console.error('initChatWidget function is not available');
      }
    }
  </script>
</body>
</html>
