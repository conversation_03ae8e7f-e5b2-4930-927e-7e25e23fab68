import React, { useState } from 'react';
import ChatWidget from './ChatWidget';
import MockChatWidget from './MockChatWidget';

interface PortalWidgetProps {
  apiKey: string;
  apiUrl: string;
  tradieName: string;
}

const PortalWidget: React.FC<PortalWidgetProps> = ({ apiKey, apiUrl, tradieName }) => {
  console.info('🎭 PortalWidget: Rendering with props:', { apiKey: apiKey?.substring(0, 10) + '...', apiUrl, tradieName });

  const [showChatWidget, setShowChatWidget] = useState(true);
  const [showDebugInfo, setShowDebugInfo] = useState(false);

  // Determine if we should use mock widget based on tradie name and environment
  const isDemoMode = tradieName === 'demo';
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // Use mock widget only for demo mode OR in development with debug enabled
  const [useMockWidget, setUseMockWidget] = useState(isDemoMode || (isLocalhost && showDebugInfo));

  return (
    <div className="portal-container">
      {/* Floating debug button */}
      <button
        onClick={() => setShowDebugInfo(!showDebugInfo)}
        style={{
          position: 'fixed',
          top: '1rem',
          right: '1rem',
          padding: '0.5rem',
          fontSize: '0.75rem',
          background: 'rgba(255,255,255,0.2)',
          border: '1px solid rgba(255,255,255,0.3)',
          borderRadius: '50%',
          color: 'white',
          cursor: 'pointer',
          minHeight: '40px',
          minWidth: '40px',
          zIndex: 1001,
          backdropFilter: 'blur(10px)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
        title="Debug Info"
      >
        🐛
      </button>

      {showDebugInfo && (
        <div style={{
          position: 'fixed',
          top: '4rem',
          right: '1rem',
          background: 'rgba(0,0,0,0.9)',
          color: 'white',
          padding: '1rem',
          borderRadius: '8px',
          fontSize: '0.75rem',
          zIndex: 1000,
          maxWidth: '300px',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255,255,255,0.2)',
          fontFamily: 'monospace'
        }}>
          <h4 style={{ margin: '0 0 0.5rem 0', fontSize: '0.875rem' }}>Debug Info</h4>
          <div><strong>Tradie:</strong> {tradieName}</div>
          <div><strong>API Key:</strong> {apiKey?.substring(0, 10)}...</div>
          <div><strong>API URL:</strong> {apiUrl}</div>
          <div><strong>Demo Mode:</strong> {isDemoMode ? 'Yes' : 'No'}</div>
          <div><strong>Localhost:</strong> {isLocalhost ? 'Yes' : 'No'}</div>
          <div><strong>Using Mock:</strong> {(isDemoMode || (isLocalhost && useMockWidget)) ? 'Yes' : 'No'}</div>
          <div style={{ marginTop: '0.5rem' }}>
            <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.75rem', marginBottom: '0.5rem' }}>
              <input
                type="checkbox"
                checked={showChatWidget}
                onChange={(e) => setShowChatWidget(e.target.checked)}
                style={{ margin: 0 }}
              />
              Show Chat Widget
            </label>
            {isLocalhost && (
              <label style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.75rem' }}>
                <input
                  type="checkbox"
                  checked={useMockWidget}
                  onChange={(e) => setUseMockWidget(e.target.checked)}
                  style={{ margin: 0 }}
                />
                Use Mock Widget
              </label>
            )}
          </div>
        </div>
      )}

      <div className="portal-chat-area">
        {showChatWidget ? (
          // Use MockChatWidget only for demo mode, otherwise use real ChatWidget
          (isDemoMode || (isLocalhost && useMockWidget)) ? (
            <MockChatWidget
              apiKey={apiKey}
              apiUrl={apiUrl}
              isPortalMode={true}
              tradieName={tradieName}
            />
          ) : (
            <ChatWidget
              apiKey={apiKey}
              apiUrl={apiUrl}
              isPortalMode={true}
              tradieName={tradieName}
            />
          )
        ) : (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            background: '#f8fafc',
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div>
              <h2>Chat Widget Hidden</h2>
              <p>Use the debug panel above to show the chat widget.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PortalWidget;
