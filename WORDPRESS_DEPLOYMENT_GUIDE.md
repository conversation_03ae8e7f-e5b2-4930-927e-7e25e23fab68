# QuoteAI Portal WordPress Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the QuoteAI portal mode to your WordPress website hosted on Hostinger. The portal will be accessible at URLs like `quoteai.com.au/chat/demo` and provide a full-screen chatbot interface.

## Deployment Options

### Option 1: Custom WordPress Page Template (Recommended)
- Best for full control and custom styling
- Requires uploading files to your theme directory
- Provides clean URLs and WordPress integration

### Option 2: Standalone HTML Pages
- Simpler deployment, just upload HTML files
- Requires manual URL routing setup
- Good for testing and quick deployment

### Option 3: WordPress Shortcode Integration
- Embed portal within existing WordPress pages
- Easy to manage through WordPress admin
- Can be combined with other content

## Prerequisites

- WordPress website hosted on Hostinger
- FTP/File Manager access to your hosting account
- Basic knowledge of WordPress themes and file structure
- Your QuoteAI API credentials

## File Structure

After deployment, your files will be organized as follows:

```
/wp-content/themes/your-theme/
├── quoteai-portal/
│   ├── portal.html              # Main portal page
│   ├── portal-demo.html         # Demo portal page
│   ├── assets/
│   │   ├── portal.js           # Portal JavaScript
│   │   ├── portal.css          # Portal styles
│   │   └── react.min.js        # React library
│   └── templates/
│       ├── page-chat.php       # WordPress template
│       └── functions-portal.php # WordPress functions
```

## Step 1: Prepare Your Files

### 1.1 Download Portal Files
- Download the production-ready portal files from this deployment package
- Extract the files to a local folder

### 1.2 Configure API Settings
Edit the portal configuration in `portal.js`:

```javascript
// Update these with your production settings
const PRODUCTION_CONFIG = {
  apiUrl: 'https://your-api-domain.com/api',
  apiKey: 'your-production-api-key',
  websiteUrl: 'quoteai.com.au'
};
```

## Step 2: Upload Files to WordPress

### 2.1 Access Your Hosting Files
1. Log into your Hostinger control panel
2. Open File Manager or connect via FTP
3. Navigate to `/public_html/wp-content/themes/your-active-theme/`

### 2.2 Create Portal Directory
1. Create a new folder called `quoteai-portal`
2. Upload all portal files to this directory
3. Ensure file permissions are set correctly (644 for files, 755 for directories)

### 2.3 Upload WordPress Integration Files
1. Copy `page-chat.php` to your theme's root directory
2. Add the portal functions to your theme's `functions.php` file

## Step 3: Configure WordPress

### 3.1 Create Chat Pages
1. Go to WordPress Admin → Pages → Add New
2. Create a page with slug `chat` (URL: `/chat/`)
3. Set the page template to "Chat Portal Template"
4. Publish the page

### 3.2 Add URL Rewrite Rules
Add this code to your theme's `functions.php`:

```php
// QuoteAI Portal URL Rewriting
function quoteai_portal_rewrite_rules() {
    add_rewrite_rule(
        '^chat/([^/]+)/?$',
        'index.php?pagename=chat&tradie_name=$matches[1]',
        'top'
    );
}
add_action('init', 'quoteai_portal_rewrite_rules');

// Add tradie_name query var
function quoteai_portal_query_vars($vars) {
    $vars[] = 'tradie_name';
    return $vars;
}
add_filter('query_vars', 'quoteai_portal_query_vars');
```

### 3.3 Flush Rewrite Rules
1. Go to WordPress Admin → Settings → Permalinks
2. Click "Save Changes" to flush rewrite rules
3. Test the URL structure

## Step 4: Test the Deployment

### 4.1 Test Portal Access
1. Visit `your-domain.com/chat/demo`
2. Verify the portal loads correctly
3. Test chat functionality
4. Test image upload features

### 4.2 Test Mobile Responsiveness
1. Open portal on mobile devices
2. Test touch interactions
3. Verify auto-expanding textarea works
4. Check safe area handling on devices with notches

### 4.3 Test Different Tradie Names
1. Try various URLs: `/chat/plumber`, `/chat/electrician`, etc.
2. Verify tradie name extraction works
3. Check page titles update correctly

## Step 5: Production Configuration

### 5.1 SSL Certificate
- Ensure your domain has a valid SSL certificate
- Update all API URLs to use HTTPS
- Test secure connections

### 5.2 Performance Optimization
- Enable WordPress caching plugins
- Optimize images and assets
- Configure CDN if available

### 5.3 Error Monitoring
- Set up error logging
- Monitor portal performance
- Configure uptime monitoring

## Troubleshooting

### Common Issues

**Portal doesn't load:**
- Check file paths and permissions
- Verify WordPress rewrite rules are active
- Check browser console for JavaScript errors

**API connection fails:**
- Verify API URL and credentials
- Check CORS settings on your API server
- Test API endpoints directly

**Mobile layout issues:**
- Clear browser cache
- Test on actual devices, not just browser dev tools
- Check viewport meta tag is present

**URL routing not working:**
- Flush WordPress permalinks
- Check .htaccess file permissions
- Verify rewrite rules are added correctly

### Debug Mode

Enable debug mode by adding this to your portal configuration:

```javascript
const DEBUG_MODE = true; // Set to false in production
```

This will show additional console logging and development tools.

## Security Considerations

1. **API Key Protection**: Never expose API keys in client-side code
2. **Input Validation**: Validate all user inputs on the server side
3. **Rate Limiting**: Implement rate limiting for API calls
4. **HTTPS Only**: Always use HTTPS in production
5. **Regular Updates**: Keep WordPress and plugins updated

## Maintenance

### Regular Tasks
- Monitor portal performance and uptime
- Update portal files when new versions are released
- Check for WordPress compatibility issues
- Review error logs regularly

### Backup Strategy
- Include portal files in your WordPress backup routine
- Test restore procedures
- Keep local copies of customized files

## Support and Updates

For technical support or updates to the portal system:
- Check the portal documentation
- Review error logs for specific issues
- Test changes in a staging environment first

## Next Steps

After successful deployment:
1. Set up analytics tracking
2. Configure custom branding
3. Add additional tradie-specific customizations
4. Implement advanced features like appointment booking
