from open_api import client
import time

def chat_with_bot(thread_id, user_input, assistant_id):
    start = time.time()
    # Adding the message to the new or existing thread.
    client.beta.threads.messages.create(thread_id=thread_id,role="user", content=user_input) # TODO: Understand what is the role for.

    # Run the thread with the new input from the user.
    run = client.beta.threads.runs.create(thread_id=thread_id, assistant_id=assistant_id)

    # While the thread runs we need to monitor its status
    while run.status in ["queued", "in_progress", "completed", "requires_action"]:
        print(f"The run object is {run}")
        print(f"Run status: {run.status}\n\n")
        if run.status == 'completed':
            break
        elif run.status == 'requires_action': 
            # function calling part.
            break

        run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
    
    messages = client.beta.threads.messages.list(thread_id=thread_id)
    print(f"Messages: {messages}")
    response = messages.data[0].content[0].text.value
    print(f"The response is {response}")
    end = time.time()
    print(f"Time required: {end-start}")
     
    return response

def post_process(full_name, suburb, contact_number, email, summary_of_issue,preferred_time):

   # email code
   # Set up your email credentials and SMTP server details
   sender_email = "<EMAIL>"
   recipient_email = email
   password = ""  # If you're using Gmail, you might need an app-specific password

   # Create the email content
   subject = "Quote AI - New Quote Request"
   body = f'''The job is for:\n\n
Full Name: {full_name}\n
Summary: {summary_of_issue}\n
Contact Number: {contact_number}\n
Suburb:{suburb}\n\n
Preferred Time: {preferred_time}\n\n
'''

   # Set up the MIME
   message = MIMEMultipart()
   message['From'] = sender_email
   message['To'] = recipient_email
   message['Subject'] = subject

   # Attach the body with the email
   message.attach(MIMEText(body, 'plain'))

   # Connect to Gmail's SMTP server and send the email
   try:
      # Establish a secure session with Gmail's SMTP server
      server = smtplib.SMTP('smtp.gmail.com', 587)
      server.starttls()  # Secure the connection
      server.login(sender_email, password)
      text = message.as_string()
      
      # Send the email
      server.sendmail(sender_email, recipient_email, text)
      print("Email sent successfully!")

   except Exception as e:
      print(f"Failed to send email: {str(e)}")

   finally:
      server.quit()
   return {"message": "Your inquiry has been sent to the plumber"}
