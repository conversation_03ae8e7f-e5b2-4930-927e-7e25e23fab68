<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuoteAI Image Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1b8ae4;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #1b8ae4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #53a2be;
        }
        .preview {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .preview img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>QuoteAI Image Upload Test</h1>

    <div class="test-section">
        <h2>1. Test File Selection</h2>
        <p>Select an image file to test the file input functionality:</p>
        <input type="file" id="fileInput" accept="image/*">
        <div class="preview" id="filePreview"></div>
        <div class="result" id="fileResult"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Camera Capture</h2>
        <p>Take a photo using your device's camera:</p>
        <button id="cameraButton">Open Camera</button>
        <input type="file" id="cameraInput" accept="image/*" capture="environment" style="display: none;">
        <div class="preview" id="cameraPreview"></div>
        <div class="result" id="cameraResult"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Image Compression</h2>
        <p>Select a large image to test compression (>8MB if possible):</p>
        <input type="file" id="compressInput" accept="image/*">
        <div class="preview" id="compressPreview"></div>
        <div class="result" id="compressResult"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Upload to Server</h2>
        <p>Select an image and upload it to the server:</p>
        <input type="file" id="uploadInput" accept="image/*">
        <button id="uploadButton">Upload Image</button>
        <div class="preview" id="uploadPreview"></div>
        <div class="result" id="uploadResult"></div>
    </div>

    <script>
        // Test 1: File Selection
        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const preview = document.getElementById('filePreview');
            const result = document.getElementById('fileResult');

            // Clear previous preview
            preview.innerHTML = '';

            // Create image preview
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            preview.appendChild(img);

            // Show file details
            result.textContent = `File selected: ${file.name}\nSize: ${(file.size / 1024).toFixed(2)} KB\nType: ${file.type}`;
        });

        // Test 2: Camera Capture
        document.getElementById('cameraButton').addEventListener('click', function() {
            document.getElementById('cameraInput').click();
        });

        document.getElementById('cameraInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const preview = document.getElementById('cameraPreview');
            const result = document.getElementById('cameraResult');

            // Clear previous preview
            preview.innerHTML = '';

            // Create image preview
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            preview.appendChild(img);

            // Show file details
            result.textContent = `Photo captured: ${file.name}\nSize: ${(file.size / 1024).toFixed(2)} KB\nType: ${file.type}`;
        });

        // Test 3: Image Compression
        document.getElementById('compressInput').addEventListener('change', async function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const preview = document.getElementById('compressPreview');
            const result = document.getElementById('compressResult');

            // Clear previous preview
            preview.innerHTML = '';
            result.textContent = 'Compressing image...';

            // Show original file details
            result.textContent = `Original file: ${file.name}\nSize: ${(file.size / 1024).toFixed(2)} KB\nType: ${file.type}\n\nCompressing...`;

            try {
                // Compress the image
                const compressedFile = await compressImage(file, 8 * 1024 * 1024); // 8MB max

                // Create image preview
                const img = document.createElement('img');
                img.src = URL.createObjectURL(compressedFile);
                preview.appendChild(img);

                // Show compressed file details
                result.textContent += `\n\nCompressed file: ${compressedFile.name}\nSize: ${(compressedFile.size / 1024).toFixed(2)} KB\nType: ${compressedFile.type}\nCompression ratio: ${(file.size / compressedFile.size).toFixed(2)}x`;
            } catch (error) {
                result.textContent += `\n\nError: ${error.message}`;
            }
        });

        // Test 4: Upload to Server
        document.getElementById('uploadInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const preview = document.getElementById('uploadPreview');

            // Clear previous preview
            preview.innerHTML = '';

            // Create image preview
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            preview.appendChild(img);
        });

        document.getElementById('uploadButton').addEventListener('click', async function() {
            const file = document.getElementById('uploadInput').files[0];
            if (!file) {
                alert('Please select a file first');
                return;
            }

            const result = document.getElementById('uploadResult');
            result.textContent = 'Uploading...';

            try {
                // Create a mock thread ID for testing
                const threadId = 'test-thread-' + Date.now();

                // Create form data
                const formData = new FormData();
                formData.append('file', file);
                formData.append('thread_id', threadId);

                // Upload to server
                const response = await fetch('http://localhost:8080/upload', {
                    method: 'POST',
                    headers: {
                        'Client-ID': 'test-client',
                        'x-api-key': 'test-key',
                    },
                    body: formData,
                });

                const data = await response.json();

                if (response.ok) {
                    result.textContent = `Upload successful!\n\nServer response:\n${JSON.stringify(data, null, 2)}`;

                    // Try to load the image from the server
                    if (data.file_url) {
                        const serverImg = document.createElement('img');
                        serverImg.src = 'http://localhost:8080' + data.file_url;
                        serverImg.alt = 'Uploaded image from server';
                        serverImg.style.border = '2px solid green';
                        document.getElementById('uploadPreview').appendChild(serverImg);
                    }
                } else {
                    result.textContent = `Upload failed!\n\nServer response:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                result.textContent = `Error: ${error.message}`;
            }
        });

        // Image compression function (same as in ChatWidget.tsx)
        async function compressImage(file, maxSizeInBytes) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = (event) => {
                    const img = new Image();
                    img.src = event.target.result;
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        let width = img.width;
                        let height = img.height;

                        // Calculate the ratio to resize
                        let quality = 0.7; // Start with 70% quality
                        const MAX_WIDTH = 1920; // Max width for reasonable image size

                        // Resize if larger than max width
                        if (width > MAX_WIDTH) {
                            const ratio = MAX_WIDTH / width;
                            width = MAX_WIDTH;
                            height = height * ratio;
                        }

                        canvas.width = width;
                        canvas.height = height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);

                        // Try to compress with different quality settings if needed
                        const tryCompress = (attemptQuality) => {
                            const dataUrl = canvas.toDataURL('image/jpeg', attemptQuality);
                            const byteString = atob(dataUrl.split(',')[1]);
                            const arrayBuffer = new ArrayBuffer(byteString.length);
                            const ia = new Uint8Array(arrayBuffer);

                            for (let i = 0; i < byteString.length; i++) {
                                ia[i] = byteString.charCodeAt(i);
                            }

                            const blob = new Blob([arrayBuffer], { type: 'image/jpeg' });
                            const compressedFile = new File([blob], file.name, { type: 'image/jpeg' });

                            if (compressedFile.size > maxSizeInBytes && attemptQuality > 0.1) {
                                // Try again with lower quality
                                tryCompress(attemptQuality - 0.1);
                            } else {
                                resolve(compressedFile);
                            }
                        };

                        tryCompress(quality);
                    };
                    img.onerror = (error) => reject(error);
                };
                reader.onerror = (error) => reject(error);
            });
        }
    </script>
</body>
</html>
