<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Chat Persistence Test</title>
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1b8ae4;
    }
    .debug-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .storage-display {
      background: #000;
      color: #0f0;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background: #0c6cb3;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border-left: 4px solid #ffd600;
    }
  </style>
  
  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the built widget -->
  <script src="chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <h1>QuoteAI Chat Persistence Test</h1>
    
    <div class="instructions">
      <h3>Instructions</h3>
      <p>This page helps you test the chat persistence functionality. Follow these steps:</p>
      <ol>
        <li>The chat widget should appear automatically at the bottom right</li>
        <li>Send a few messages and upload an image</li>
        <li>Click the "Simulate Page Refresh" button below</li>
        <li>Verify that your chat history and images are still visible</li>
        <li>You can also close this tab and reopen it to test real persistence</li>
      </ol>
    </div>
    
    <div class="debug-panel">
      <h2>Test Controls</h2>
      <button id="refreshButton">Simulate Page Refresh</button>
      <button id="clearStorage">Clear All Storage</button>
      <button id="checkStatus">Check Widget Status</button>
      
      <div class="storage-display" id="storageDisplay"></div>
    </div>
  </div>

  <script>
    // Function to display localStorage info
    function updateStorageDisplay() {
      const storageDisplay = document.getElementById('storageDisplay');
      const items = { ...localStorage };
      
      let html = '<strong>localStorage Contents:</strong><br><br>';
      
      if (Object.keys(items).length === 0) {
        html += 'No items in localStorage<br>';
      } else {
        for (const key in items) {
          if (key.startsWith('quoteai_')) {
            try {
              const value = JSON.parse(items[key]);
              const messageCount = value.messages ? value.messages.length : 0;
              const expiryDate = value.expiresAt ? new Date(value.expiresAt).toLocaleString() : 'N/A';
              
              html += `<strong>${key}</strong>:<br>`;
              html += `- Messages: ${messageCount}<br>`;
              html += `- Thread ID: ${value.threadId || 'None'}<br>`;
              html += `- Expires: ${expiryDate}<br><br>`;
              
              // Show first message if available
              if (value.messages && value.messages.length > 0) {
                const firstMsg = value.messages[0];
                html += `First message: "${firstMsg.content.substring(0, 30)}${firstMsg.content.length > 30 ? '...' : ''}"<br><br>`;
              }
            } catch (e) {
              html += `<strong>${key}</strong>: Error parsing JSON<br>`;
            }
          }
        }
      }
      
      html += `<br><strong>initChatWidget available:</strong> ${typeof window.initChatWidget === 'function' ? 'Yes ✅' : 'No ❌'}<br>`;
      
      storageDisplay.innerHTML = html;
    }
    
    // Initialize the widget
    function initializeWidget() {
      if (typeof window.initChatWidget === 'function') {
        const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 9);
        window.initChatWidget({
          clientId: "test-client-final",
          apiKey: "test-key-final",
          uuid: clientUUID,
          apiUrl: "http://localhost:8080",
        });
        console.log('Widget initialized with UUID:', clientUUID);
        return true;
      } else {
        console.error('initChatWidget is not available');
        return false;
      }
    }
    
    // Set up event listeners
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Page loaded');
      
      // Initialize widget
      const initialized = initializeWidget();
      
      // Update storage display
      updateStorageDisplay();
      
      // Set up refresh button
      document.getElementById('refreshButton').addEventListener('click', () => {
        window.location.reload();
      });
      
      // Set up clear storage button
      document.getElementById('clearStorage').addEventListener('click', () => {
        localStorage.clear();
        updateStorageDisplay();
        alert('All localStorage data cleared. Refresh the page to start fresh.');
      });
      
      // Set up check status button
      document.getElementById('checkStatus').addEventListener('click', () => {
        updateStorageDisplay();
        alert(`Widget initialization status: ${initialized ? 'Success' : 'Failed'}`);
      });
      
      // Update storage display every 2 seconds
      setInterval(updateStorageDisplay, 2000);
    });
  </script>
</body>
</html>
