<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Layout Fix Validation - Admin Interface</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }
        
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            z-index: 10000;
            max-width: 250px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .test-header {
            font-weight: bold;
            margin-bottom: 10px;
            color: #4ade80;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 5px;
        }
        
        .test-checklist {
            list-style: none;
            padding: 0;
            margin: 10px 0 0 0;
        }
        
        .test-checklist li {
            margin: 6px 0;
            padding: 4px 0;
            display: flex;
            align-items: center;
            font-size: 11px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
            flex-shrink: 0;
        }
        
        .status-pass { background: #10b981; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
        
        .viewport-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 10px;
            z-index: 10000;
        }
        
        .test-controls {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        .test-btn:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="test-panel">
        <div class="test-header">🔧 Layout Fix Validation</div>
        <ul class="test-checklist">
            <li><span class="status-indicator status-pending"></span>Contact Step Visibility</li>
            <li><span class="status-indicator status-pending"></span>Step Line Connection</li>
            <li><span class="status-indicator status-pending"></span>All Steps Layout</li>
            <li><span class="status-indicator status-pending"></span>Scrolling Behavior</li>
            <li><span class="status-indicator status-pending"></span>Button Accessibility</li>
            <li><span class="status-indicator status-pending"></span>Mobile Responsive</li>
        </ul>
        <div class="test-controls">
            <button class="test-btn" onclick="runAllTests()">Run Tests</button>
            <button class="test-btn" onclick="simulateMobile()">Mobile View</button>
        </div>
    </div>
    
    <div class="viewport-info">
        <span id="viewport-size">Viewport: Loading...</span>
    </div>
    
    <!-- Admin app will be rendered here -->
    <div id="admin-root"></div>

    <!-- Load React and ReactDOM from CDN -->
    <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/react-router-dom@6.8.0/dist/umd/react-router-dom.development.js"></script>
    
    <!-- Load the admin interface -->
    <script src="./dist/admin-interface.es.js" type="module"></script>
    <link rel="stylesheet" href="./dist/styles/admin-D6150Mqb.css">
    
    <script>
        // Test results tracking
        const testResults = {
            contactStepVisibility: false,
            stepLineConnection: false,
            allStepsLayout: false,
            scrollingBehavior: false,
            buttonAccessibility: false,
            mobileResponsive: false
        };
        
        function updateTestStatus(test, status) {
            testResults[test] = status;
            const indicators = document.querySelectorAll('.test-checklist li');
            const testIndex = Object.keys(testResults).indexOf(test);
            if (indicators[testIndex]) {
                const indicator = indicators[testIndex].querySelector('.status-indicator');
                indicator.className = `status-indicator status-${status ? 'pass' : 'fail'}`;
            }
        }
        
        // Viewport size tracking
        function updateViewportInfo() {
            const viewportInfo = document.getElementById('viewport-size');
            if (viewportInfo) {
                viewportInfo.textContent = `Viewport: ${window.innerWidth}x${window.innerHeight}`;
            }
        }
        
        window.addEventListener('resize', updateViewportInfo);
        updateViewportInfo();
        
        // Test 1: Contact Step Visibility
        function testContactStepVisibility() {
            setTimeout(() => {
                // Navigate to step 2 if possible
                const nextButton = document.querySelector('.btn-primary');
                if (nextButton && nextButton.textContent.includes('Next')) {
                    nextButton.click();
                    
                    setTimeout(() => {
                        const stepContent = document.querySelector('.step-content');
                        const formElements = document.querySelectorAll('input, textarea, select, button');
                        let allVisible = true;
                        
                        formElements.forEach(element => {
                            const rect = element.getBoundingClientRect();
                            const isVisible = (
                                rect.top >= 0 &&
                                rect.bottom <= window.innerHeight &&
                                rect.left >= 0 &&
                                rect.right <= window.innerWidth
                            );
                            
                            if (!isVisible && element.offsetParent !== null) {
                                allVisible = false;
                            }
                        });
                        
                        updateTestStatus('contactStepVisibility', allVisible);
                    }, 500);
                }
            }, 1000);
        }
        
        // Test 2: Step Line Connection
        function testStepLineConnection() {
            setTimeout(() => {
                const stepConnectors = document.querySelectorAll('.step-connector');
                const stepLines = document.querySelectorAll('.step-line');
                
                const hasConnectors = stepConnectors.length > 0;
                const hasVisibleLines = Array.from(stepConnectors).some(connector => {
                    const styles = window.getComputedStyle(connector);
                    return styles.display !== 'none' && styles.visibility !== 'hidden';
                });
                
                updateTestStatus('stepLineConnection', hasConnectors && hasVisibleLines);
            }, 1000);
        }
        
        // Test 3: All Steps Layout
        function testAllStepsLayout() {
            setTimeout(() => {
                const stepActions = document.querySelectorAll('.step-actions');
                const stepForms = document.querySelectorAll('.step-form');
                
                let layoutCorrect = true;
                
                // Check if step-actions are outside step-form
                stepActions.forEach(actions => {
                    const isInsideForm = actions.closest('.step-form') !== null;
                    if (isInsideForm) {
                        layoutCorrect = false;
                    }
                });
                
                updateTestStatus('allStepsLayout', layoutCorrect && stepActions.length > 0);
            }, 1000);
        }
        
        // Test 4: Scrolling Behavior
        function testScrollingBehavior() {
            setTimeout(() => {
                const stepContent = document.querySelector('.step-content');
                if (stepContent) {
                    const styles = window.getComputedStyle(stepContent);
                    const hasScrolling = styles.overflowY === 'auto' || styles.overflowY === 'scroll';
                    const hasMinHeight = styles.minHeight === '0px';
                    
                    updateTestStatus('scrollingBehavior', hasScrolling && hasMinHeight);
                }
            }, 1000);
        }
        
        // Test 5: Button Accessibility
        function testButtonAccessibility() {
            setTimeout(() => {
                const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
                let allAccessible = true;
                
                buttons.forEach(button => {
                    const rect = button.getBoundingClientRect();
                    const isAccessible = (
                        rect.bottom <= window.innerHeight &&
                        rect.top >= 0 &&
                        rect.height >= 44 // Minimum touch target
                    );
                    
                    if (!isAccessible) {
                        allAccessible = false;
                    }
                });
                
                updateTestStatus('buttonAccessibility', allAccessible && buttons.length > 0);
            }, 1000);
        }
        
        // Test 6: Mobile Responsive
        function testMobileResponsive() {
            const isMobile = window.innerWidth <= 768;
            
            setTimeout(() => {
                if (isMobile) {
                    const mobileIndicator = document.querySelector('.mobile-step-indicator');
                    const stepList = document.querySelector('.step-list');
                    
                    const mobileOptimized = (
                        (mobileIndicator && window.getComputedStyle(mobileIndicator).display !== 'none') ||
                        (stepList && window.getComputedStyle(stepList).display === 'none')
                    );
                    
                    updateTestStatus('mobileResponsive', mobileOptimized);
                } else {
                    updateTestStatus('mobileResponsive', true);
                }
            }, 1000);
        }
        
        // Run all tests
        function runAllTests() {
            console.log('🔧 Running layout fix validation tests...');
            testContactStepVisibility();
            testStepLineConnection();
            testAllStepsLayout();
            testScrollingBehavior();
            testButtonAccessibility();
            testMobileResponsive();
        }
        
        // Simulate mobile view
        function simulateMobile() {
            // This would typically require dev tools, but we can test responsive behavior
            console.log('📱 Testing mobile responsive behavior...');
            testMobileResponsive();
        }
        
        // Auto-run tests when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 2000);
        });
        
        // Re-run tests on resize
        window.addEventListener('resize', () => {
            setTimeout(() => {
                testMobileResponsive();
                testButtonAccessibility();
            }, 500);
        });
        
        // Console logging
        console.log('🔧 Layout Fix Validation Suite Loaded');
        console.log('📱 Viewport:', window.innerWidth + 'x' + window.innerHeight);
        console.log('🔍 Tests will run automatically in 2 seconds');
        
        // Error tracking
        window.addEventListener('error', (e) => {
            console.error('❌ Error detected:', e.error);
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            console.error('❌ Unhandled promise rejection:', e.reason);
        });
    </script>
</body>
</html>
