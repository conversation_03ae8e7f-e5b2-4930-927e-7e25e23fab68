# Message Suggestions Feature

This document outlines the implementation of the message suggestions feature in the QuoteAI chatbot.

## Overview

The message suggestions feature displays clickable suggestion bubbles under the bot's messages, providing users with quick options for their next message. This improves the user experience by making it easier to interact with the chatbot.

## Client-Side Implementation

The client-side implementation includes:

1. Updated types to support suggestions
2. UI components for displaying suggestion bubbles
3. Logic for handling suggestion clicks
4. Default suggestions for the initial greeting

## Server-Side Implementation (To Be Implemented)

To fully implement this feature, a new endpoint needs to be added to the server:

### New Endpoint: `/suggestions`

**Request:**
```json
{
  "thread_id": "thread_123456",
  "context": "Previous message content for context"
}
```

**Response:**
```json
{
  "suggestions": [
    {
      "text": "Display text",
      "value": "Full message to send"
    },
    ...
  ]
}
```

### Integration with OpenAI

The server should use OpenAI to generate contextually relevant suggestions based on the conversation history. Here's a suggested implementation approach:

1. Retrieve the conversation history for the given thread_id
2. Use OpenAI to generate 3-4 relevant suggestions based on the conversation context
3. Format the suggestions as per the response format above
4. Return the suggestions to the client

### Alternative Implementation

If implementing a new endpoint is not feasible in the short term, you can modify the existing `/chat` endpoint to include suggestions in its response:

```json
{
  "response": "Bot's response message",
  "suggestions": [
    {
      "text": "Display text",
      "value": "Full message to send"
    },
    ...
  ]
}
```

## Default Suggestions

The client has default suggestions that will be used if the server doesn't provide any:

```javascript
[
  { text: "Get a quote", value: "I'd like to get a quote for your services." },
  { text: "Service areas", value: "What areas do you service?" },
  { text: "Pricing", value: "Can you tell me about your pricing?" },
  { text: "Availability", value: "When are you available?" }
]
```

## Testing

To test this feature:
1. Open the chat widget
2. Verify that suggestion bubbles appear under the initial greeting message
3. Click on a suggestion and verify that it sends the correct message
4. Check that the suggestions are appropriate for the conversation context

## Future Enhancements

Potential future enhancements include:
- Dynamic suggestions based on user behavior and common questions
- Personalized suggestions based on user history
- A/B testing different suggestion sets to optimize engagement
