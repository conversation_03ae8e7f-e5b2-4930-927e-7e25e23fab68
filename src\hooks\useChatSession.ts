import { useState, useEffect, useCallback, useRef } from 'react';
import { chatStorage } from '../utils/ChatStorage';
import { initiateConversationApi } from '../services/apiService';
import { WELCOME_MESSAGE_CONTENT, DEFAULT_SUGGESTIONS, ERROR_MESSAGES } from '../constants';
import type { Message } from '../types';

export interface UseChatSessionReturn {
  sessionId: string | null;
  threadId: string | null;
  initialMessages: Message[];
  isLoading: boolean;
  error: string | null;
  initializeOrRestoreSession: () => Promise<void>;
  clearCurrentChatSession: () => void;
  setThreadIdForSession: (newThreadId: string) => void;
  isSessionInitialized: boolean;
}

export const useChatSession = (apiKey: string, customApiUrl?: string): UseChatSessionReturn => {
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [threadId, setThreadId] = useState<string | null>(null);
  const [initialMessages, setInitialMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isSessionInitialized, setIsSessionInitialized] = useState<boolean>(false);

  // Track if initialization is in progress to prevent race conditions
  const initializingRef = useRef<boolean>(false);

  const initializeOrRestoreSession = useCallback(async () => {
    // Prevent multiple simultaneous initializations
    if (initializingRef.current) {
      console.log('Initialization already in progress, skipping...');
      return;
    }

    initializingRef.current = true;
    setIsLoading(true);
    setError(null);
    try {
      const currentSid = chatStorage.initSession(); // Creates new or gets existing valid
      setSessionId(currentSid);

      const storedThreadId = chatStorage.getThreadId(currentSid);
      const storedMessages = chatStorage.getMessages(currentSid);

      if (storedThreadId && storedMessages.length > 0) {
        console.log('Restoring existing session with thread_id:', storedThreadId);
        setThreadId(storedThreadId);
        setInitialMessages(storedMessages);
      } else {
        // No valid stored session with threadId or messages, or new session
        console.log('Creating new conversation thread...');
        const response = await initiateConversationApi(apiKey, customApiUrl);
        if (response.success === "true" && response.thread_id) {
          console.log('New thread_id created:', response.thread_id);
          setThreadId(response.thread_id);
          chatStorage.saveThreadId(currentSid, response.thread_id);

          const welcomeMessage: Message = {
            type: 'bot',
            content: WELCOME_MESSAGE_CONTENT,
            timestamp: Date.now(),
            suggestions: DEFAULT_SUGGESTIONS,
          };
          setInitialMessages([welcomeMessage]);
          chatStorage.saveMessages(currentSid, [welcomeMessage]); // Save welcome msg for new session
        } else {
          throw new Error('Failed to obtain thread_id from server.');
        }
      }
      setIsSessionInitialized(true);
    } catch (err: any) {
      console.error('Error initializing session:', err);
      setError(err.message || ERROR_MESSAGES.INIT_FAILED);
      // Fallback: if API fails, still provide a session ID for local storage but no thread ID
      if (!sessionId) {
          const fallbackSid = chatStorage.initSession(); // Ensure a session ID exists
          setSessionId(fallbackSid);
      }
    } finally {
      setIsLoading(false);
      initializingRef.current = false;
    }
  }, [apiKey, customApiUrl]); // Removed sessionId and isLoading from dependencies to prevent re-runs

  const clearCurrentChatSession = useCallback(async () => {
    console.log('Clearing current chat session...');
    if (sessionId) {
      chatStorage.clearSession(sessionId);
    }
    setSessionId(null);
    setThreadId(null);
    setInitialMessages([]);
    setError(null);
    setIsSessionInitialized(false);

    // Re-initialize instead of reloading the page
    await initializeOrRestoreSession();
  }, [sessionId, initializeOrRestoreSession]);

  const setThreadIdForSession = useCallback((newThreadId: string) => {
    if (sessionId) {
      setThreadId(newThreadId);
      chatStorage.saveThreadId(sessionId, newThreadId);
    }
  }, [sessionId]);

  // Effect for session expiry check on window focus
  useEffect(() => {
    const handleWindowFocus = () => {
      if (sessionId && chatStorage.isSessionExpired(sessionId)) {
        clearCurrentChatSession(); // This will trigger a reload and re-initialization
      }
    };
    window.addEventListener('focus', handleWindowFocus);
    return () => window.removeEventListener('focus', handleWindowFocus);
  }, [sessionId, clearCurrentChatSession]);

  return {
    sessionId,
    threadId,
    initialMessages,
    isLoading,
    error,
    initializeOrRestoreSession,
    clearCurrentChatSession,
    setThreadIdForSession,
    isSessionInitialized,
  };
};