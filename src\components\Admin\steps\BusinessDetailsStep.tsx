/**
 * Business Details Step
 * Third step of the onboarding wizard - tradie type and service areas
 */

import React from 'react';
import { motion } from 'framer-motion';
import { useFormContext } from 'react-hook-form';
import TagInput from '../TagInput';
import { getErrorMessage, hasError } from '../utils/formUtils';

const tradieSpecializations = [
  'Bathroom renovation', 'Kitchen plumbing', 'Blocked drains', 'Tap repairs',
  'Toilet repairs', 'Hot water systems', 'Gas fitting', 'Pipe repairs',
  'Leak detection', 'Emergency plumbing', 'Commercial plumbing', 'Residential plumbing',
  'Water heater installation', 'Sewer line repair', 'Fixture installation',
  'Pipe relining', 'Backflow prevention', 'Water filtration', 'Grease trap cleaning',
  'Storm water drainage'
];

interface BusinessDetailsStepProps {
  loading: boolean;
  nextStep?: () => void;
  previousStep?: () => void;
}

const BusinessDetailsStep: React.FC<BusinessDetailsStepProps> = ({
  loading,
  nextStep,
  previousStep
}) => {
  const { register, formState: { errors }, watch, setValue, trigger } = useFormContext();

  const stepVariants = {
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  };

  const handleNext = async () => {
    // Validate current step fields
    const isValid = await trigger(['tradie_details.tradie_type']);
    if (isValid && nextStep) {
      nextStep();
    }
  };

  const handlePrevious = () => {
    if (previousStep) {
      previousStep();
    }
  };

  const specializations = watch('tradie_details.specializations') || [];
  const serviceSuburbs = watch('tradie_details.service_areas.suburbs') || [];
  const servicePostcodes = watch('tradie_details.service_areas.postcodes') || [];

  const handleSuburbsChange = (value: string) => {
    const suburbs = value.split(',').map(s => s.trim()).filter(s => s);
    setValue('tradie_details.service_areas.suburbs', suburbs);
  };

  const handlePostcodesChange = (value: string) => {
    const postcodes = value.split(',').map(s => s.trim()).filter(s => s);
    setValue('tradie_details.service_areas.postcodes', postcodes);
  };

  return (
    <motion.div
      className="step-content"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      transition={{ duration: 0.3 }}
    >
      <div className="step-header">
        <h2>Business Details</h2>
        <p>Tell us about your trade and service areas</p>
      </div>

      <div className="step-form">
        <div className="form-section">
          <h3>Trade Information</h3>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="tradie_type">
                Tradie Type *
              </label>
              <select
                id="tradie_type"
                {...register('tradie_details.tradie_type', {
                  required: 'Tradie type is required'
                })}
                disabled={loading}
                className={hasError(errors, 'tradie_details.tradie_type') ? 'error' : ''}
              >
                <option value="">Select Tradie Type</option>
                <option value="plumber">Plumber</option>
                <option value="electrician">Electrician</option>
                <option value="carpenter">Carpenter</option>
                <option value="painter">Painter</option>
                <option value="landscaper">Landscaper</option>
                <option value="roofer">Roofer</option>
                <option value="tiler">Tiler</option>
                <option value="general">General Contractor</option>
              </select>
              {hasError(errors, 'tradie_details.tradie_type') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'tradie_details.tradie_type')}
                </motion.span>
              )}
            </div>

            <div className="form-group form-group-full">
              <label htmlFor="specializations">
                Specializations *
              </label>
              <TagInput
                value={specializations}
                onChange={(specs) => setValue('tradie_details.specializations', specs)}
                placeholder="Type and press Enter to add specializations..."
                disabled={loading}
                maxTags={8}
                suggestions={tradieSpecializations}
              />
              {hasError(errors, 'tradie_details.specializations') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  At least one specialization is required
                </motion.span>
              )}
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>Service Areas</h3>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="service_suburbs">
                Service Suburbs
              </label>
              <input
                type="text"
                id="service_suburbs"
                value={serviceSuburbs.join(', ')}
                onChange={(e) => handleSuburbsChange(e.target.value)}
                placeholder="Sydney, Parramatta, Liverpool"
                disabled={loading}
              />
              <small className="field-help">Separate multiple suburbs with commas</small>
            </div>

            <div className="form-group">
              <label htmlFor="service_postcodes">
                Service Postcodes
              </label>
              <input
                type="text"
                id="service_postcodes"
                value={servicePostcodes.join(', ')}
                onChange={(e) => handlePostcodesChange(e.target.value)}
                placeholder="2000, 2150, 2170"
                disabled={loading}
              />
              <small className="field-help">Separate multiple postcodes with commas</small>
            </div>

            <div className="form-group">
              <label htmlFor="radius_km">
                Service Radius (km)
              </label>
              <input
                type="number"
                id="radius_km"
                {...register('tradie_details.service_areas.radius_km', {
                  min: { value: 1, message: 'Minimum radius is 1km' },
                  max: { value: 500, message: 'Maximum radius is 500km' }
                })}
                min="1"
                max="500"
                disabled={loading}
                className={hasError(errors, 'tradie_details.service_areas.radius_km') ? 'error' : ''}
              />
              {hasError(errors, 'tradie_details.service_areas.radius_km') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'tradie_details.service_areas.radius_km')}
                </motion.span>
              )}
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>Business Hours</h3>
          <div className="business-hours-grid">
            {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
              <div key={day} className="business-hours-row">
                <label className="day-label">
                  {day.charAt(0).toUpperCase() + day.slice(1)}
                </label>
                <div className="time-inputs">
                  <input
                    type="time"
                    {...register(`tradie_details.business_hours.${day}.open`)}
                    disabled={loading}
                  />
                  <span>to</span>
                  <input
                    type="time"
                    {...register(`tradie_details.business_hours.${day}.close`)}
                    disabled={loading}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="step-actions">
        <button
          type="button"
          onClick={handlePrevious}
          className="btn-secondary"
          disabled={loading}
        >
          Previous
        </button>

        <button
          type="button"
          onClick={handleNext}
          className="btn-primary"
          disabled={loading}
        >
          {loading ? (
            <span className="loading-content">
              <span className="loading-spinner"></span>
              Loading...
            </span>
          ) : (
            'Next Step'
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default BusinessDetailsStep;
