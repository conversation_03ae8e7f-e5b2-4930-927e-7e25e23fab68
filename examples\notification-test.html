<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Notification Test</title>

  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1b8ae4;
    }
    .test-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .control-panel {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #0c6cb3;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border-left: 4px solid #ffd600;
    }

    /* Custom notification bubble styles (in case the imported CSS doesn't work) */
    .notification-bubble {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #ff4b4b; /* Red color for notifications */
      color: white;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      animation: notification-pulse 1.5s infinite;
    }

    /* Notification pulse animation */
    @keyframes notification-pulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 75, 75, 0.7);
      }
      70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(255, 75, 75, 0);
      }
      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 75, 75, 0);
      }
    }
  </style>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

  <!-- Load the built widget -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
</head>
<body>
  <div class="container">
    <h1>QuoteAI Notification Test</h1>

    <div class="instructions">
      <h3>Instructions</h3>
      <p>This page helps you test the notification bubble and loading indicator functionality:</p>
      <ol>
        <li>The chat widget should appear automatically at the bottom right</li>
        <li>Click the "Close Chat" button below to close the chat window</li>
        <li>Click the "Send Test Message" button to simulate receiving a message</li>
        <li>You should see a notification bubble with a number and hear a sound</li>
        <li>Click the "Test Loading" button to simulate a loading state</li>
        <li>You should see a loading dots animation in the top right corner of the chat button</li>
        <li>Click the chat button to open the chat and verify the notification disappears</li>
      </ol>
    </div>

    <div class="test-panel">
      <h2>Test Controls</h2>
      <div class="control-panel">
        <button id="closeChat">Close Chat</button>
        <button id="sendMessage">Send Test Message</button>
        <button id="testLoading">Test Loading</button>
        <button id="clearStorage">Clear Storage</button>
      </div>
    </div>
  </div>
  <style>
    /* Force the chat button to be fixed at bottom right */
    .chat-button {
      position: fixed !important;
      bottom: 20px !important;
      right: 20px !important;
      z-index: 9999 !important;
      left: auto !important;
    }
  </style>
  <script>
    // Initialize the widget
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Page loaded');

      if (typeof window.initChatWidget === 'function') {
        const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 9);
        window.initChatWidget({
          clientId: "test-client-notification",
          apiKey: "test-key-notification",
          uuid: clientUUID,
          apiUrl: "http://localhost:8080",
        });
        console.log('Widget initialized with UUID:', clientUUID);
      } else {
        console.error('initChatWidget is not available');
      }

      // Set up test controls
      document.getElementById('closeChat').addEventListener('click', () => {
        // Find the close button in the chat header and click it
        const closeButton = document.querySelector('.close-button');
        if (closeButton) {
          closeButton.click();
          console.log('Chat closed');
        } else {
          console.error('Close button not found');
        }
      });

      // Add a click event listener to the chat button to reset unread count
      setTimeout(() => {
        const chatButton = document.querySelector('.chat-button');
        if (chatButton) {
          const originalClickHandler = chatButton.onclick;

          chatButton.onclick = function(event) {
            // Call the original click handler
            if (originalClickHandler) {
              originalClickHandler.call(this, event);
            }

            // Remove notification bubble
            const bubble = chatButton.querySelector('.notification-bubble');
            if (bubble) {
              bubble.remove();
            }

            console.log('Chat opened, notification reset');
          };
        }
      }, 1000); // Wait for the widget to initialize

      document.getElementById('sendMessage').addEventListener('click', async () => {
        // Close the chat if it's open
        const chatContainer = document.querySelector('.chat-container.open');
        if (chatContainer) {
          const closeButton = document.querySelector('.close-button');
          if (closeButton) {
            closeButton.click();
            // Wait for animation to complete
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        // Find the chat button
        const chatButton = document.querySelector('.chat-button');
        if (!chatButton) {
          console.error('Chat button not found');
          return;
        }

        // Get the current unread count
        let currentCount = 0;
        const existingBubble = chatButton.querySelector('.notification-bubble');
        if (existingBubble) {
          currentCount = parseInt(existingBubble.textContent) || 0;
        }

        // Increment the count
        const newCount = currentCount + 1;

        // Create or update the notification bubble
        if (existingBubble) {
          // Update existing bubble
          existingBubble.textContent = newCount;
          // Force animation restart by changing the key
          existingBubble.setAttribute('key', `notification-${Date.now()}`);
        } else {
          // Create new bubble
          const bubble = document.createElement('span');
          bubble.className = 'notification-bubble';
          bubble.textContent = newCount;
          bubble.setAttribute('key', `notification-${Date.now()}`);
          chatButton.appendChild(bubble);
        }

        // Play notification sound
        try {
          const audio = new Audio('data:audio/mp3;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFTb25vdGhlcXVlLm9yZwBURU5DAAAAHQAAA1N3aXRjaCBQbHVzIMKpIE5DSCBTb2Z0d2FyZQBUSVQyAAAABgAAAzIyMzUAVFNTRQAAAA8AAANMYXZmNTcuODMuMTAwAAAAAAAAAAAAAAD/80DEAAAAA0gAAAAATEFNRTMuMTAwVVVVVVVVVVVVVUxBTUUzLjEwMFVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQsRbAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVf/zQMSkAAADSAAAAABVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV');
          audio.volume = 0.5;
          audio.play();
        } catch (error) {
          console.error('Error playing notification sound:', error);
        }

        console.log('Test notification sent, count:', newCount);
      });

      // Add test loading button functionality
      document.getElementById('testLoading').addEventListener('click', async () => {
        // Close the chat if it's open
        const chatContainer = document.querySelector('.chat-container.open');
        if (chatContainer) {
          const closeButton = document.querySelector('.close-button');
          if (closeButton) {
            closeButton.click();
            // Wait for animation to complete
            await new Promise(resolve => setTimeout(resolve, 500));
          }
        }

        // Find the chat button
        const chatButton = document.querySelector('.chat-button');
        if (!chatButton) {
          console.error('Chat button not found');
          return;
        }

        // Add loading indicator
        let loadingIndicator = chatButton.querySelector('.button-loading-indicator');
        if (!loadingIndicator) {
          loadingIndicator = document.createElement('span');
          loadingIndicator.className = 'button-loading-indicator';

          // Add three dots
          for (let i = 0; i < 3; i++) {
            const dot = document.createElement('span');
            dot.className = 'dot';
            loadingIndicator.appendChild(dot);
          }

          chatButton.appendChild(loadingIndicator);
        }

        console.log('Loading indicator added');

        // Remove loading indicator after 3 seconds
        setTimeout(() => {
          if (loadingIndicator && loadingIndicator.parentNode) {
            loadingIndicator.parentNode.removeChild(loadingIndicator);
            console.log('Loading indicator removed');
          }
        }, 3000);
      });

      document.getElementById('clearStorage').addEventListener('click', () => {
        localStorage.clear();
        console.log('Storage cleared');
        alert('Storage cleared. Refresh the page to start fresh.');
      });
    });
  </script>
  <!-- Additional styles to ensure the chat button is fixed at bottom right -->
  <style>
    /* Force the chat button to be fixed at bottom right */
    .chat-button {
      position: fixed !important;
      bottom: 20px !important;
      right: 20px !important;
      z-index: 9999 !important;
      left: auto !important;
    }
  </style>
</body>
</html>
