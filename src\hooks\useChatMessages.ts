import { useState, useEffect, useCallback, useRef } from 'react';
import { sendMessageApi } from '../services/apiService';
import { chatStorage } from '../utils/ChatStorage';
import { ERROR_MESSAGES, DEFAULT_SUGGESTIONS } from '../constants';
import type { Message, Suggestion } from '../types';

interface UseChatMessagesReturn {
  messages: Message[];
  isSending: boolean;
  sendMessageError: string | null;
  sendUserMessage: (
    text: string,
    attachmentUrls?: string[],
    isRetry?: boolean
  ) => Promise<void>;
  addBotMessage: (content: string, suggestions?: Suggestion[]) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  clearSendMessageError: () => void;
}

// Queue for messages that need to be sent sequentially
interface MessageQueueItem {
  text: string;
  attachmentUrls?: string[];
  threadId: string; // Ensure threadId is captured at the time of queuing
  retries: number;
}

const MAX_RETRIES = 2;
const RETRY_DELAY_MS = 2000;

export const useChatMessages = (
  apiKey: string,
  sessionId: string | null,
  threadId: string | null,
  initialMessages: Message[],
  customApiUrl?: string
): UseChatMessagesReturn => {
  const [messages, setMessages] = useState<Message[]>(initialMessages);
  const [isSending, setIsSending] = useState<boolean>(false);
  const [sendMessageError, setSendMessageError] = useState<string | null>(null);

  const messageQueueRef = useRef<MessageQueueItem[]>([]);
  const isProcessingQueueRef = useRef<boolean>(false);

  // Update messages from initialMessages when session changes
  useEffect(() => {
    setMessages(initialMessages);
  }, [initialMessages]);

  // Save messages to storage whenever they change
  useEffect(() => {
    if (sessionId && messages.length > 0) {
      chatStorage.saveMessages(sessionId, messages);
    }
  }, [messages, sessionId]);

  const addBotMessage = useCallback((content: string, suggestions?: Suggestion[]) => {
    const botMessage: Message = {
      type: 'bot',
      content,
      timestamp: Date.now(),
      suggestions: suggestions || (messages.length === 1 ? DEFAULT_SUGGESTIONS : []), // Default suggestions for first bot message
    };
    setMessages(prev => [...prev, botMessage]);
  }, [messages.length]);


  const processMessageQueue = useCallback(async () => {
    if (isProcessingQueueRef.current || messageQueueRef.current.length === 0) {
      return;
    }
    isProcessingQueueRef.current = true;
    setIsSending(true); // Global sending indicator for UI

    const messageToSend = messageQueueRef.current[0];

    try {
      setSendMessageError(null);
      const response = await sendMessageApi(
        apiKey,
        messageToSend.threadId, // Use threadId captured at queue time
        messageToSend.text,
        messageToSend.attachmentUrls || [],
        customApiUrl
      );
      addBotMessage(response.response, response.suggestions);
      messageQueueRef.current.shift(); // Remove successfully sent message
    } catch (err: any) {
      console.error('Error sending message from queue:', err);
      if ((err.status === 429 || err.status === 409) && messageToSend.retries < MAX_RETRIES) {
        messageToSend.retries += 1;
        // Keep in queue and retry after a delay
        setTimeout(() => {
            isProcessingQueueRef.current = false; // Allow next attempt
            processMessageQueue();
        }, RETRY_DELAY_MS * messageToSend.retries); // Exponential backoff
        setSendMessageError(err.message || ERROR_MESSAGES.SERVER_BUSY); // Show temporary error
        // Do not shift from queue yet
        setIsSending(false); // Potentially allow other UI interactions if not strictly sequential
        isProcessingQueueRef.current = false; 
        return; // Exit to wait for retry
      } else {
        // Max retries reached or other error
        setSendMessageError(err.message || ERROR_MESSAGES.SEND_FAILED);
        addBotMessage(ERROR_MESSAGES.SEND_FAILED + ` (Details: ${err.message || 'Unknown error'})`);
        messageQueueRef.current.shift(); // Remove unsendable message
      }
    }

    isProcessingQueueRef.current = false;
    if (messageQueueRef.current.length > 0) {
      setTimeout(processMessageQueue, 500); // Small delay before processing next
    } else {
      setIsSending(false); // No more messages in queue
    }
  }, [apiKey, customApiUrl, addBotMessage]);


  const sendUserMessage = useCallback(async (
    text: string,
    attachmentUrls: string[] = [],
  ) => {
    if (!threadId) {
      setSendMessageError("Cannot send message: Chat session not fully initialized.");
      addBotMessage("I'm having trouble connecting. Please try refreshing.");
      return;
    }

    const userMessage: Message = {
      type: 'user',
      content: text,
      attachments: attachmentUrls.length > 0 ? attachmentUrls : undefined,
      timestamp: Date.now(),
    };
    setMessages(prev => [...prev, userMessage]);
    setSendMessageError(null);

    messageQueueRef.current.push({ text, attachmentUrls, threadId, retries: 0 });
    if (!isProcessingQueueRef.current) {
      processMessageQueue();
    }
  }, [threadId, processMessageQueue, addBotMessage]);
  
  const clearSendMessageError = useCallback(() => {
    setSendMessageError(null);
  }, []);

  return {
    messages,
    isSending,
    sendMessageError,
    sendUserMessage,
    addBotMessage,
    setMessages,
    clearSendMessageError,
  };
};