<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Admin - Test</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: Arial, sans-serif;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }
    
    .test-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      padding: 40px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .status {
      padding: 12px 16px;
      border-radius: 8px;
      margin: 10px 0;
      font-weight: 500;
    }
    
    .success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .info {
      background: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
    
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 12px 24px;
      font-size: 14px;
      cursor: pointer;
      margin: 10px 5px;
      transition: background-color 0.2s ease;
    }
    
    button:hover {
      background: #1570c7;
    }
    
    #admin-root {
      margin-top: 20px;
      min-height: 400px;
      border: 2px dashed #ddd;
      border-radius: 8px;
      padding: 20px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>🔐 QuoteAI Admin Interface Test</h1>
    <p>This page tests the admin interface routing and functionality.</p>
    
    <div id="status-container">
      <div class="status info">
        <strong>Status:</strong> Initializing admin interface...
      </div>
    </div>
    
    <div>
      <button onclick="testRouting()">Test Routing</button>
      <button onclick="testFirebase()">Test Firebase Config</button>
      <button onclick="clearStatus()">Clear Status</button>
    </div>
    
    <!-- Admin app will be rendered here -->
    <div id="admin-root"></div>
  </div>

  <!-- Load admin interface -->
  <script type="module" src="/dist/admin-interface.es.js"></script>
  <link rel="stylesheet" href="/dist/admin-_NKhJJsU.css">

  <script>
    function addStatus(message, type = 'info') {
      const container = document.getElementById('status-container');
      const status = document.createElement('div');
      status.className = `status ${type}`;
      status.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
      container.appendChild(status);
    }
    
    function clearStatus() {
      const container = document.getElementById('status-container');
      container.innerHTML = '<div class="status info"><strong>Status:</strong> Cleared</div>';
    }
    
    function testRouting() {
      addStatus('Testing React Router hash navigation...', 'info');
      
      // Test hash routing
      if (window.location.hash) {
        addStatus(`Current hash: ${window.location.hash}`, 'success');
      } else {
        addStatus('No hash detected, setting default route...', 'info');
        window.location.hash = '#/onboard';
      }
    }
    
    function testFirebase() {
      addStatus('Testing Firebase configuration...', 'info');
      
      // Check if Firebase is loaded
      if (typeof firebase !== 'undefined') {
        addStatus('Firebase SDK loaded successfully', 'success');
      } else {
        addStatus('Firebase SDK not detected (this is expected for module build)', 'info');
      }
    }
    
    // Monitor admin interface loading
    setTimeout(() => {
      const adminRoot = document.getElementById('admin-root');
      if (adminRoot && adminRoot.children.length > 0) {
        addStatus('✅ Admin interface loaded successfully!', 'success');
      } else {
        addStatus('⚠️ Admin interface may not have loaded properly', 'error');
      }
    }, 3000);
    
    // Monitor hash changes
    window.addEventListener('hashchange', () => {
      addStatus(`Route changed to: ${window.location.hash}`, 'info');
    });
    
    // Initial status
    addStatus('Test page loaded, admin interface should appear below', 'success');
  </script>
</body>
</html>
