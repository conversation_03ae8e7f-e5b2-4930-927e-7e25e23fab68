<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QuoteAI Firebase Storage Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #1b8ae4;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            background-color: #1b8ae4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #53a2be;
        }
        .preview {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .preview img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>QuoteAI Firebase Storage Test</h1>

    <div class="test-section">
        <h2>Test Firebase Storage Upload</h2>
        <p>Select an image and upload it to Firebase Storage:</p>
        <input type="file" id="uploadInput" accept="image/*">
        <button id="uploadButton">Upload to Firebase Storage</button>
        <div class="preview" id="uploadPreview"></div>
        <div class="result" id="uploadResult"></div>
    </div>

    <script>
        // Test Firebase Storage Upload
        document.getElementById('uploadInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (!file) return;

            const preview = document.getElementById('uploadPreview');

            // Clear previous preview
            preview.innerHTML = '';

            // Create image preview
            const img = document.createElement('img');
            img.src = URL.createObjectURL(file);
            preview.appendChild(img);
        });

        document.getElementById('uploadButton').addEventListener('click', async function() {
            const file = document.getElementById('uploadInput').files[0];
            if (!file) {
                alert('Please select a file first');
                return;
            }

            const result = document.getElementById('uploadResult');
            result.textContent = 'Uploading to Firebase Storage...';

            try {
                // Create a mock thread ID for testing
                const threadId = 'test-thread-' + Date.now();

                // Create form data
                const formData = new FormData();
                formData.append('file', file);
                formData.append('thread_id', threadId);

                // Upload to server
                const response = await fetch('http://localhost:8080/upload', {
                    method: 'POST',
                    headers: {
                        'Client-ID': 'test-client',
                        'x-api-key': 'test-key',
                    },
                    body: formData,
                });

                const data = await response.json();

                if (response.ok) {
                    result.textContent = `Upload successful!\n\nServer response:\n${JSON.stringify(data, null, 2)}`;

                    // Display the image from Firebase Storage
                    if (data.file_url) {
                        const firebaseImg = document.createElement('img');
                        firebaseImg.src = data.file_url;
                        firebaseImg.alt = 'Uploaded image from Firebase Storage';
                        firebaseImg.style.border = '2px solid green';
                        document.getElementById('uploadPreview').appendChild(firebaseImg);
                    }
                } else {
                    result.textContent = `Upload failed!\n\nServer response:\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                result.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
