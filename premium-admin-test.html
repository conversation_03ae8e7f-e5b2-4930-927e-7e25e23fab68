<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Admin Interface - UX Test</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }
        
        .test-overlay {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 11px;
            z-index: 10000;
            max-width: 200px;
            backdrop-filter: blur(10px);
        }
        
        .test-checklist {
            list-style: none;
            padding: 0;
            margin: 8px 0 0 0;
        }
        
        .test-checklist li {
            margin: 4px 0;
            padding: 2px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-checklist li:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 6px;
        }
        
        .status-pass { background: #10b981; }
        .status-fail { background: #ef4444; }
        .status-pending { background: #f59e0b; }
        
        .viewport-info {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 10px;
            z-index: 10000;
        }
    </style>
</head>
<body>
    <div class="test-overlay">
        <strong>🎨 Premium UX Test</strong>
        <ul class="test-checklist">
            <li><span class="status-indicator status-pending"></span>Layout Visibility</li>
            <li><span class="status-indicator status-pending"></span>Step Indicators</li>
            <li><span class="status-indicator status-pending"></span>Form Fields</li>
            <li><span class="status-indicator status-pending"></span>Animations</li>
            <li><span class="status-indicator status-pending"></span>Touch Targets</li>
            <li><span class="status-indicator status-pending"></span>Responsiveness</li>
        </ul>
    </div>
    
    <div class="viewport-info">
        <span id="viewport-size">Viewport: Loading...</span>
    </div>
    
    <!-- Admin app will be rendered here -->
    <div id="admin-root"></div>

    <!-- Load React and ReactDOM from CDN -->
    <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/react-router-dom@6.8.0/dist/umd/react-router-dom.development.js"></script>
    
    <!-- Load the admin interface -->
    <script src="./dist/admin-interface.es.js" type="module"></script>
    <link rel="stylesheet" href="./dist/styles/admin-ClFLr22Z.css">
    
    <script>
        // Viewport size tracking
        function updateViewportInfo() {
            const viewportInfo = document.getElementById('viewport-size');
            if (viewportInfo) {
                viewportInfo.textContent = `Viewport: ${window.innerWidth}x${window.innerHeight}`;
            }
        }
        
        window.addEventListener('resize', updateViewportInfo);
        updateViewportInfo();
        
        // UX Testing Functions
        const testResults = {
            layoutVisibility: false,
            stepIndicators: false,
            formFields: false,
            animations: false,
            touchTargets: false,
            responsiveness: false
        };
        
        function updateTestStatus(test, status) {
            testResults[test] = status;
            const indicators = document.querySelectorAll('.test-checklist li');
            const testIndex = Object.keys(testResults).indexOf(test);
            if (indicators[testIndex]) {
                const indicator = indicators[testIndex].querySelector('.status-indicator');
                indicator.className = `status-indicator status-${status ? 'pass' : 'fail'}`;
            }
        }
        
        // Test layout visibility
        function testLayoutVisibility() {
            setTimeout(() => {
                const wizardContainer = document.querySelector('.wizard-container');
                const stepActions = document.querySelector('.step-actions');
                const submitButton = document.querySelector('.btn-primary');
                
                if (wizardContainer && stepActions && submitButton) {
                    const containerRect = wizardContainer.getBoundingClientRect();
                    const buttonRect = submitButton.getBoundingClientRect();
                    
                    const isVisible = (
                        containerRect.top >= 0 &&
                        containerRect.bottom <= window.innerHeight &&
                        buttonRect.bottom <= window.innerHeight
                    );
                    
                    updateTestStatus('layoutVisibility', isVisible);
                }
            }, 1000);
        }
        
        // Test step indicators
        function testStepIndicators() {
            setTimeout(() => {
                const stepCircles = document.querySelectorAll('.step-circle');
                const stepTitles = document.querySelectorAll('.step-title');
                
                const hasIcons = Array.from(stepCircles).some(circle => 
                    circle.querySelector('.step-icon')
                );
                
                const noDescriptions = document.querySelectorAll('.step-description').length === 0;
                
                updateTestStatus('stepIndicators', hasIcons && noDescriptions && stepCircles.length > 0);
            }, 1000);
        }
        
        // Test form fields
        function testFormFields() {
            setTimeout(() => {
                const inputs = document.querySelectorAll('input:not(.tag-input), textarea, select');
                let hasCorrectBackground = true;
                
                inputs.forEach(input => {
                    const styles = window.getComputedStyle(input);
                    const bgColor = styles.backgroundColor;
                    
                    // Check if background is light gray instead of black
                    if (bgColor === 'rgb(0, 0, 0)' || bgColor === 'black') {
                        hasCorrectBackground = false;
                    }
                });
                
                updateTestStatus('formFields', hasCorrectBackground && inputs.length > 0);
            }, 1000);
        }
        
        // Test touch targets
        function testTouchTargets() {
            setTimeout(() => {
                const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
                const inputs = document.querySelectorAll('input, textarea, select');
                
                let allTargetsValid = true;
                
                [...buttons, ...inputs].forEach(element => {
                    const rect = element.getBoundingClientRect();
                    if (rect.height < 44 || rect.width < 44) {
                        allTargetsValid = false;
                    }
                });
                
                updateTestStatus('touchTargets', allTargetsValid);
            }, 1000);
        }
        
        // Test responsiveness
        function testResponsiveness() {
            const originalWidth = window.innerWidth;
            
            // Test mobile breakpoint
            if (window.innerWidth <= 768) {
                setTimeout(() => {
                    const mobileIndicator = document.querySelector('.mobile-step-indicator');
                    const stepList = document.querySelector('.step-list');
                    
                    updateTestStatus('responsiveness', 
                        (mobileIndicator && mobileIndicator.style.display !== 'none') ||
                        (stepList && window.getComputedStyle(stepList).display === 'none')
                    );
                }, 1000);
            } else {
                updateTestStatus('responsiveness', true);
            }
        }
        
        // Test animations
        function testAnimations() {
            setTimeout(() => {
                const animatedElements = document.querySelectorAll('.step-circle, .btn-primary');
                let hasTransitions = true;
                
                animatedElements.forEach(element => {
                    const styles = window.getComputedStyle(element);
                    if (!styles.transition || styles.transition === 'none') {
                        hasTransitions = false;
                    }
                });
                
                updateTestStatus('animations', hasTransitions);
            }, 1000);
        }
        
        // Run all tests
        function runUXTests() {
            testLayoutVisibility();
            testStepIndicators();
            testFormFields();
            testTouchTargets();
            testResponsiveness();
            testAnimations();
        }
        
        // Start testing when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runUXTests, 2000);
        });
        
        // Re-run tests on resize
        window.addEventListener('resize', () => {
            setTimeout(runUXTests, 500);
        });
        
        // Console logging for debugging
        console.log('🎨 Premium UX Test Suite Loaded');
        console.log('📱 Viewport:', window.innerWidth + 'x' + window.innerHeight);
        console.log('🔍 Tests will run automatically in 2 seconds');
        
        // Error tracking
        window.addEventListener('error', (e) => {
            console.error('❌ Error detected:', e.error);
        });
        
        window.addEventListener('unhandledrejection', (e) => {
            console.error('❌ Unhandled promise rejection:', e.reason);
        });
    </script>
</body>
</html>
