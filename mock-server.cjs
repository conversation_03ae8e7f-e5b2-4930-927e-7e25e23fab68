const http = require('http');
const url = require('url');

const PORT = 8080;

// Mock responses
const mockResponses = {
  initiate_conversation: {
    success: true,
    thread_id: `thread_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  },
  chat: {
    response: "Thank you for your message! This is a mock response from the test server. How can I help you today?",
    suggestions: [
      { text: "Get a quote", value: "I'd like to get a quote for your services." },
      { text: "Service areas", value: "What areas do you service?" },
      { text: "Pricing", value: "Can you tell me about your pricing?" }
    ]
  },
  upload: {
    success: true,
    file_url: "/uploads/mock_file.jpg",
    filename: "mock_file.jpg"
  }
};

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        resolve({});
      }
    });
    req.on('error', reject);
  });
}

// CORS headers
function setCorsHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-api-key, Client-ID');
  res.setHeader('Access-Control-Max-Age', '86400');
}

const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} - ${method} ${path}`);

  // Set CORS headers for all requests
  setCorsHeaders(res);

  // Handle preflight requests
  if (method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    if (method === 'POST' && path === '/initiate_conversation') {
      // Handle conversation initiation
      const body = await parseBody(req);
      console.log('Initiate conversation request body:', body);
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(mockResponses.initiate_conversation));
      
    } else if (method === 'POST' && path === '/chat') {
      // Handle chat messages
      const body = await parseBody(req);
      console.log('Chat request body:', body);
      
      // Simulate some processing time
      setTimeout(() => {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify(mockResponses.chat));
      }, 500);
      
    } else if (method === 'POST' && path === '/upload') {
      // Handle file uploads
      console.log('Upload request received');
      
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(mockResponses.upload));
      
    } else {
      // Handle 404
      res.writeHead(404, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Endpoint not found' }));
    }
  } catch (error) {
    console.error('Server error:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ error: 'Internal server error' }));
  }
});

server.listen(PORT, () => {
  console.log(`Mock API server running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  POST /initiate_conversation - Initialize a new conversation');
  console.log('  POST /chat - Send a chat message');
  console.log('  POST /upload - Upload a file');
});

// Handle server shutdown gracefully
process.on('SIGINT', () => {
  console.log('\nShutting down mock server...');
  server.close(() => {
    console.log('Mock server stopped.');
    process.exit(0);
  });
});
