# QuoteAI Portal Mode

The QuoteAI Portal Mode provides a full-screen chatbot experience accessible via direct URLs like `quoteai.com/chat/[tradie-name]`. This is an alternative to the embeddable widget mode, offering a dedicated portal interface for each tradie.

## Features

### 🚀 Core Features
- **Full-screen chat interface** - Dedicated portal page instead of widget overlay
- **URL-based routing** - Access via `quoteai.com/chat/[tradie-name]`
- **Connection monitoring** - Real-time connection status with offline handling
- **Error recovery** - Automatic retry mechanisms and graceful error handling
- **Modern UI** - Animated, responsive design with smooth transitions
- **Mobile-first** - Optimized for all device sizes

### 🛡️ Failsafe Features
- **Offline mode** - Portal continues to function when server is unreachable
- **Connection retry** - Automatic reconnection attempts with exponential backoff
- **Error messages** - Clear feedback when messages fail to send
- **Graceful degradation** - Portal loads even if some features fail

### 🎨 Visual Features
- **Smooth animations** - Loading screens, transitions, and micro-interactions
- **Status indicators** - Online/offline connection status
- **Modern design** - Gradient backgrounds, glassmorphism effects
- **Interactive elements** - Hover effects, button animations

## Development Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Modern browser with JavaScript enabled

### Quick Start

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev:portal
   ```
   This opens the portal test environment at `http://localhost:5173/portal-test.html`

3. **Test portal URLs**
   - Development: `http://localhost:5173/chat.html?tradie=test-tradie`
   - Production format: `http://localhost:5173/chat/test-tradie` (requires server routing)

### Available Scripts

```bash
# Development
npm run dev:portal          # Start dev server with portal test page
npm run test:portal          # Alias for dev:portal

# Building
npm run build               # Build both widget and portal
npm run build:portal        # Build portal only (future enhancement)

# Preview
npm run preview:portal      # Preview built portal
```

## Testing

### Test Environment

Access the comprehensive test environment at `/portal-test.html` which includes:

- **Multiple tradie examples** - Test different business names
- **Error scenarios** - Test missing tradie names and connection failures
- **Responsive testing** - Test on different screen sizes
- **Browser compatibility** - Test across different browsers

### Manual Testing Checklist

#### ✅ Functionality Tests
- [ ] Portal loads correctly with tradie name
- [ ] Chat functionality works (send/receive messages)
- [ ] Image upload and preview works
- [ ] Connection status updates correctly
- [ ] Error handling displays appropriate messages
- [ ] Offline mode functions properly

#### 📱 Responsive Tests
- [ ] Desktop (1200px+) - Full layout with all features
- [ ] Tablet (768px-1199px) - Adapted layout
- [ ] Mobile (320px-767px) - Mobile-optimized interface
- [ ] Landscape/Portrait orientations

#### 🌐 Browser Tests
- [ ] Chrome/Chromium
- [ ] Firefox
- [ ] Safari
- [ ] Edge
- [ ] Mobile browsers (iOS Safari, Chrome Mobile)

#### ⚡ Performance Tests
- [ ] Fast loading times (<3 seconds)
- [ ] Smooth animations (60fps)
- [ ] No memory leaks during extended use
- [ ] Graceful handling of slow connections

## URL Formats

### Production URLs
```
https://quoteai.com/chat/aquaflow-plumbers
https://quoteai.com/chat/sparky-electrical
https://quoteai.com/chat/handy-home-repairs
```

### Development URLs
```
http://localhost:5173/chat.html?tradie=aquaflow-plumbers
http://localhost:5173/chat.html?tradie=sparky-electrical
http://localhost:5173/chat.html?tradie=handy-home-repairs
```

## Architecture

### File Structure
```
src/
├── portal.tsx              # Portal entry point
├── PortalChatWidget.tsx     # Main portal component
├── hooks/
│   └── usePortalMode.ts     # Portal-specific logic
└── styles/
    └── portal.css           # Portal-specific styles

public/
├── portal.html              # Production portal template
├── chat.html                # Development portal page
└── portal-test.html         # Test environment
```

### Key Components

1. **portal.tsx** - Entry point that extracts tradie name and initializes portal
2. **PortalChatWidget.tsx** - Main portal component with full-screen layout
3. **usePortalMode.ts** - Hook for connection monitoring and portal-specific logic
4. **portal.css** - Comprehensive styling for portal mode

## Configuration

### Environment Variables
```bash
VITE_API_URL=http://localhost:8080    # Backend API URL
VITE_API_KEY=your-api-key             # API authentication key
```

### Portal Configuration
The portal automatically configures itself based on the tradie name in the URL:
- `tradieName` - Extracted from URL path or query parameter
- `websiteUrl` - Uses tradie name as website identifier
- `customer_name` - Uses tradie name for backend identification

## Deployment

### Production Deployment
1. Build the project: `npm run build`
2. Deploy `dist/` folder to your web server
3. Configure server routing to serve `portal.html` for `/chat/*` routes
4. Update API URLs in production environment

### Server Configuration Example (Nginx)
```nginx
location /chat/ {
    try_files $uri $uri/ /portal.html;
}
```

## Troubleshooting

### Common Issues

**Portal doesn't load**
- Check browser console for JavaScript errors
- Verify React dependencies are loaded
- Ensure portal.tsx is being served correctly

**Tradie name not detected**
- Verify URL format matches expected pattern
- Check browser console for extraction errors
- Test with query parameter format in development

**Connection issues**
- Check API URL configuration
- Verify backend server is running
- Test with browser network tab open

**Styling issues**
- Ensure portal.css is loaded
- Check for CSS conflicts with existing styles
- Verify responsive breakpoints

### Debug Mode

In development, use keyboard shortcuts:
- `Ctrl/Cmd + T` - Open test environment
- `Ctrl/Cmd + D` - Log debug information to console

## Contributing

When contributing to portal mode:

1. Test on multiple devices and browsers
2. Ensure accessibility standards are met
3. Verify error handling works correctly
4. Update tests and documentation
5. Follow the existing code style and patterns

## License

Same as the main QuoteAI project.
