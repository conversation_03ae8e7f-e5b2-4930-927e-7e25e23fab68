from db import connection

def authenticate(api_key, domain=None):
    """Authenticate a request using API key and optionally domain.

    Args:
        api_key (str): The API key from the request
        domain (str, optional): The domain/website URL from the request

    Returns:
        dict or False: Client data if authenticated, False otherwise
    """
    # For backward compatibility, allow the hardcoded API key
    if api_key == ";C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL":
        return True

    # If no domain provided, we can only check the API key
    if not domain:
        return False

    # Get Firestore client
    fb_client = connection.get_db_client_prod()

    # Look up client by domain and API key
    clients = fb_client.collection("clients").where("website_url", "==", domain).where("api_key", "==", api_key).limit(1).get()

    # Check if we found a matching client
    for client in clients:
        return client.to_dict()

    return False