# QuoteAI Widget Integration Guide

This guide explains how to integrate the QuoteAI chatbot widget into your website using the new combined domain verification and API key authentication method.

## Prerequisites

- A valid API key from QuoteAI
- Your domain registered with QuoteAI

## Basic Integration

### 1. Include Required Dependencies

The QuoteAI widget requires <PERSON><PERSON> and <PERSON>act<PERSON><PERSON> as dependencies. Include them in your HTML before loading the widget:

```html
<!-- Include React and ReactDOM (required dependencies) -->
<script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
<script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

<!-- Include the QuoteAI widget script and styles -->
<script src="https://widget.quoteai.com/chatbot-widget.umd.js"></script>
<link rel="stylesheet" href="https://widget.quoteai.com/style.css">
```

For production environments, use the minified versions of <PERSON><PERSON> and <PERSON>actDOM:

```html
<script src="https://unpkg.com/react@18.0.0/umd/react.production.min.js"></script>
<script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.production.min.js"></script>
```

### 2. Initialize the Widget

Add a script to initialize the widget when the page loads:

```html
<script>
  document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.initChatWidget === 'function') {
      window.initChatWidget({
        apiKey: "YOUR_API_KEY",
        websiteUrl: "your-domain.com", // Your website domain
        apiUrl: "https://api.quoteai.com" // Optional: API URL override
      });
    }
  });
</script>
```

Replace `YOUR_API_KEY` with the API key provided by QuoteAI, and `your-domain.com` with your registered domain.

## Configuration Options

The `initChatWidget` function accepts the following parameters:

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `apiKey` | String | Yes | Your unique API key for authentication |
| `websiteUrl` | String | Yes | Your website domain for domain verification |
| `apiUrl` | String | No | Override the default API URL (optional) |

## Legacy Support

For backward compatibility, the widget also supports the old initialization method:

```javascript
window.initChatWidget({
  clientId: "your-domain.com",
  apiKey: "YOUR_API_KEY",
  uuid: "your-domain.com",
  apiUrl: "https://api.quoteai.com" // Optional
});
```

However, we recommend using the new method with `websiteUrl` for better security and clarity.

## Troubleshooting

### Widget Not Loading

If the widget fails to load, check the following:

1. **React and ReactDOM**: Make sure React and ReactDOM are loaded before the widget script.
2. **Script Order**: The widget script must be loaded before initializing it.
3. **Console Errors**: Check your browser's console for any JavaScript errors.

### Authentication Issues

If the widget loads but fails to connect to the server:

1. **API Key**: Verify that your API key is correct.
2. **Domain Registration**: Ensure your domain is registered with QuoteAI.
3. **CORS Issues**: If you're getting CORS errors, contact QuoteAI support.

## Advanced Usage

### Customizing the Widget Appearance

You can customize the widget's appearance using CSS. The widget uses the following CSS classes:

- `.chat-widget-container`: The main container
- `.chat-button`: The chat button
- `.chat-window`: The chat window
- `.chat-header`: The chat header
- `.chat-messages`: The messages container
- `.chat-input`: The input area

Example:

```css
.chat-button {
  background-color: #ff5722 !important; /* Custom button color */
}

.chat-header {
  background-color: #ff5722 !important; /* Custom header color */
}
```

### Programmatically Controlling the Widget

You can programmatically control the widget using the global `window.quoteAIWidget` object:

```javascript
// Open the chat window
if (window.quoteAIWidget && window.quoteAIWidget.openChat) {
  window.quoteAIWidget.openChat();
}

// Close the chat window
if (window.quoteAIWidget && window.quoteAIWidget.closeChat) {
  window.quoteAIWidget.closeChat();
}
```

## Support

If you encounter any issues or have questions, please contact QuoteAI <NAME_EMAIL>.
