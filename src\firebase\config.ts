/**
 * Firebase configuration for the admin interface
 */

import { initializeApp } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCKreWz7I_w0UVnP1Tsu_C7_8wsFbruxBM",
  authDomain: "quoteai-firebase.firebaseapp.com",
  projectId: "quoteai-firebase",
  storageBucket: "quoteai-firebase.firebasestorage.app",
  messagingSenderId: "7929508717",
  appId: "1:7929508717:web:9b36c03fe6ec990c846064",
  measurementId: "G-JQZWSRL3XP"
};
// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Cloud Firestore and get a reference to the service
export const db = getFirestore(app);

export default app;
