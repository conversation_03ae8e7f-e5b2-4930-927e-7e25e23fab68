# QuoteAI Front-End Documentation

Welcome to the QuoteAI front-end documentation. This repository contains comprehensive documentation for the QuoteAI chat widget, a React-based component designed to be embedded in client websites to provide instant quote services.

## Documentation Files

This documentation consists of the following files:

1. [**FRONTEND_DOCUMENTATION.md**](./FRONTEND_DOCUMENTATION.md) - Comprehensive overview of the front-end architecture, components, and development workflow.

2. [**DESIGN_DOCUMENTATION.md**](./DESIGN_DOCUMENTATION.md) - Visual guide to the UI components, styling, and design principles.

3. [**INTEGRATION_GUIDE.md**](./INTEGRATION_GUIDE.md) - Step-by-step instructions for integrating the widget into client websites.

## Quick Start

### For Developers

If you're a developer working on the QuoteAI widget:

1. Review the [FRONTEND_DOCUMENTATION.md](./FRONTEND_DOCUMENTATION.md) to understand the project structure and components.
2. Set up your development environment:
   ```
   npm install
   npm run dev
   ```
3. Make changes following the design guidelines in [DESIGN_DOCUMENTATION.md](./DESIGN_DOCUMENTATION.md).

### For Clients

If you're a client looking to integrate the QuoteAI widget into your website:

1. Follow the step-by-step instructions in [INTEGRATION_GUIDE.md](./INTEGRATION_GUIDE.md).
2. Contact support if you encounter any issues.

## Project Overview

QuoteAI is a chat widget that provides instant quote services through an AI-powered chatbot. The widget is designed to be:

- **Lightweight**: Minimal impact on website performance
- **Responsive**: Works on all device sizes
- **Customizable**: Can be adapted to match client branding
- **User-friendly**: Simple and intuitive interface

## Key Features

- Floating chat button that expands to a full chat interface
- Real-time conversation with AI-powered responses
- Persistent conversations using user identifiers
- Responsive design for all device sizes
- Simple integration with any website

## Screenshots

### Chat Button (Closed State)
![Chat Button](https://via.placeholder.com/350x100/1b8ae4/FFFFFF?text=Chat+Button)

### Chat Widget (Open State)
![Chat Widget](https://via.placeholder.com/350x560/FFFFFF/000000?text=Chat+Widget)