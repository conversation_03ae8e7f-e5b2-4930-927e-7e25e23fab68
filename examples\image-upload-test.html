<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Enhanced Image Upload Test</title>
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1b8ae4;
    }
    .test-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border-left: 4px solid #ffd600;
    }
    .feature-list {
      margin-top: 15px;
    }
    .feature-list li {
      margin-bottom: 8px;
    }
  </style>
  
  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the built widget -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
</head>
<body>
  <div class="container">
    <h1>QuoteAI Enhanced Image Upload Test</h1>
    
    <div class="instructions">
      <h3>Instructions</h3>
      <p>This page helps you test the enhanced image upload functionality:</p>
      <ol>
        <li>Click the chat button to open the chat widget</li>
        <li>Click the paperclip icon to upload images</li>
        <li>Try uploading multiple images (up to 8)</li>
        <li>Test the image preview grid</li>
        <li>Click on images to expand them</li>
        <li>Try removing individual images</li>
        <li>Test the "Clear All" button</li>
      </ol>
      
      <div class="feature-list">
        <h4>New Features:</h4>
        <ul>
          <li><strong>Multi-image Upload:</strong> Upload up to 8 images at once</li>
          <li><strong>Image Grid:</strong> View all uploaded images in a grid layout</li>
          <li><strong>"Add Image" Button:</strong> Quickly add more images to your selection</li>
          <li><strong>Image Counter:</strong> See how many images you've added out of the maximum</li>
          <li><strong>Clear All Button:</strong> Remove all images at once</li>
          <li><strong>Image Expansion:</strong> Click on any image to view it in full size</li>
          <li><strong>Responsive Design:</strong> Works well on mobile devices</li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    // Initialize the widget
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Page loaded');
      
      if (typeof window.initChatWidget === 'function') {
        const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 9);
        window.initChatWidget({
          clientId: "test-client-images",
          apiKey: "test-key-images",
          uuid: clientUUID,
          apiUrl: "http://localhost:8080",
        });
        console.log('Widget initialized with UUID:', clientUUID);
      } else {
        console.error('initChatWidget is not available');
      }
    });
  </script>
</body>
</html>
