<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Message Queue Test</title>
  
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    h1 {
      color: #1b8ae4;
    }
    .test-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .control-panel {
      display: flex;
      gap: 10px;
      margin-bottom: 15px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background: #0c6cb3;
    }
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      border-left: 4px solid #ffd600;
    }
    .feature-list {
      margin-top: 15px;
    }
    .feature-list li {
      margin-bottom: 8px;
    }
    .message-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-top: 15px;
    }
    .message-item {
      background: #e3f2fd;
      padding: 10px;
      border-radius: 4px;
      font-size: 14px;
    }
    .status {
      margin-top: 10px;
      padding: 10px;
      background: #f0f0f0;
      border-radius: 4px;
      font-size: 14px;
    }
    .error {
      color: #d32f2f;
      font-weight: bold;
    }
    .success {
      color: #388e3c;
      font-weight: bold;
    }
  </style>
  
  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the built widget -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
</head>
<body>
  <div class="container">
    <h1>QuoteAI Message Queue Test</h1>
    
    <div class="instructions">
      <h3>Instructions</h3>
      <p>This page helps you test the message queue and debounce functionality:</p>
      <ol>
        <li>Click the chat button to open the chat widget</li>
        <li>Try sending multiple messages in quick succession</li>
        <li>Notice how the messages are queued and processed one at a time</li>
        <li>Observe the queue indicator showing pending messages</li>
        <li>Try using the new send button</li>
      </ol>
      
      <div class="feature-list">
        <h4>New Features:</h4>
        <ul>
          <li><strong>Message Queue:</strong> Messages are queued and processed one at a time</li>
          <li><strong>Debounce:</strong> Prevents sending multiple messages too quickly</li>
          <li><strong>Queue Indicator:</strong> Shows how many messages are waiting to be processed</li>
          <li><strong>Send Button:</strong> New button to send messages</li>
          <li><strong>Loading State:</strong> Visual feedback when messages are being processed</li>
          <li><strong>Error Handling:</strong> Better error handling with retries for server busy errors</li>
        </ul>
      </div>
    </div>
    
    <div class="test-panel">
      <h3>Quick Message Test</h3>
      <p>Click the button below to simulate sending multiple messages in quick succession:</p>
      <div class="control-panel">
        <button id="openChatBtn">1. Open Chat</button>
        <button id="sendMultipleBtn" disabled>2. Send 5 Messages Quickly</button>
        <button id="clearStorageBtn">Clear Storage</button>
      </div>
      <div id="statusDisplay" class="status">Status: Waiting for actions...</div>
      <div id="messageList" class="message-list"></div>
    </div>
  </div>

  <script>
    // Initialize the widget
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Page loaded');
      
      const statusDisplay = document.getElementById('statusDisplay');
      const openChatBtn = document.getElementById('openChatBtn');
      const sendMultipleBtn = document.getElementById('sendMultipleBtn');
      const clearStorageBtn = document.getElementById('clearStorageBtn');
      const messageList = document.getElementById('messageList');
      
      let chatInitialized = false;
      let chatOpen = false;
      
      if (typeof window.initChatWidget === 'function') {
        const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 9);
        window.initChatWidget({
          clientId: "test-client-queue",
          apiKey: "test-key-queue",
          uuid: clientUUID,
          apiUrl: "http://localhost:8080",
        });
        console.log('Widget initialized with UUID:', clientUUID);
        chatInitialized = true;
        statusDisplay.innerHTML = "Status: <span class='success'>Widget initialized successfully</span>";
      } else {
        console.error('initChatWidget is not available');
        statusDisplay.innerHTML = "Status: <span class='error'>Widget initialization failed</span>";
      }
      
      // Function to check if chat is open
      function isChatOpen() {
        return document.querySelector('.chat-container.open') !== null;
      }
      
      // Function to wait for an element to be available
      function waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
          const startTime = Date.now();
          
          const checkElement = () => {
            const element = document.querySelector(selector);
            if (element) {
              resolve(element);
              return;
            }
            
            if (Date.now() - startTime > timeout) {
              reject(new Error(`Element ${selector} not found within ${timeout}ms`));
              return;
            }
            
            setTimeout(checkElement, 100);
          };
          
          checkElement();
        });
      }
      
      // Set up open chat button
      openChatBtn.addEventListener('click', async () => {
        try {
          statusDisplay.innerHTML = "Status: Opening chat...";
          
          // Find and click the chat button
          const chatButton = document.querySelector('.chat-button');
          if (chatButton) {
            chatButton.click();
            statusDisplay.innerHTML = "Status: <span class='success'>Chat opened</span>";
            chatOpen = true;
            
            // Enable the send multiple button
            sendMultipleBtn.disabled = false;
          } else {
            statusDisplay.innerHTML = "Status: <span class='error'>Chat button not found</span>";
          }
        } catch (error) {
          console.error('Error opening chat:', error);
          statusDisplay.innerHTML = `Status: <span class='error'>Error opening chat: ${error.message}</span>`;
        }
      });
      
      // Set up send multiple button
      sendMultipleBtn.addEventListener('click', async () => {
        try {
          if (!isChatOpen()) {
            statusDisplay.innerHTML = "Status: <span class='error'>Chat is not open. Please open the chat first.</span>";
            return;
          }
          
          statusDisplay.innerHTML = "Status: Preparing to send messages...";
          
          // Wait for chat input and send button to be available
          const chatInput = await waitForElement('.chat-input input');
          const sendButton = await waitForElement('.send-button');
          
          if (!chatInput || !sendButton) {
            statusDisplay.innerHTML = "Status: <span class='error'>Chat input or send button not found</span>";
            return;
          }
          
          // Sample messages
          const messages = [
            "Hello, I need a quote",
            "I have a leaking pipe",
            "How soon can you come?",
            "Is there a call-out fee?",
            "Do you work on weekends?"
          ];
          
          // Add messages to the message list
          messageList.innerHTML = '';
          messages.forEach(msg => {
            const msgItem = document.createElement('div');
            msgItem.className = 'message-item';
            msgItem.textContent = msg;
            messageList.appendChild(msgItem);
          });
          
          statusDisplay.innerHTML = "Status: <span class='success'>Sending messages...</span>";
          
          // Send messages in quick succession
          let index = 0;
          const sendInterval = setInterval(() => {
            if (index < messages.length) {
              // Set input value
              chatInput.value = messages[index];
              
              // Dispatch input event to update React state
              const event = new Event('input', { bubbles: true });
              chatInput.dispatchEvent(event);
              
              // Click send button
              sendButton.click();
              
              // Update status
              statusDisplay.innerHTML = `Status: <span class='success'>Sent message ${index + 1} of ${messages.length}</span>`;
              
              index++;
            } else {
              clearInterval(sendInterval);
              statusDisplay.innerHTML = "Status: <span class='success'>All messages sent! Watch the queue processing.</span>";
            }
          }, 300); // Send a message every 300ms
        } catch (error) {
          console.error('Error sending messages:', error);
          statusDisplay.innerHTML = `Status: <span class='error'>Error sending messages: ${error.message}</span>`;
        }
      });
      
      // Set up clear storage button
      clearStorageBtn.addEventListener('click', () => {
        localStorage.clear();
        statusDisplay.innerHTML = "Status: <span class='success'>Storage cleared</span>";
        alert('Storage cleared. Refresh the page to start fresh.');
      });
      
      // Check if chat is already open
      setTimeout(() => {
        if (isChatOpen()) {
          chatOpen = true;
          sendMultipleBtn.disabled = false;
          statusDisplay.innerHTML = "Status: <span class='success'>Chat is already open</span>";
        }
      }, 1000);
    });
  </script>
</body>
</html>
