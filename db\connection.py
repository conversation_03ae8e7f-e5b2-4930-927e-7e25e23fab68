import firebase_admin
from firebase_admin import credentials
from firebase_admin import firestore
from firebase_admin import storage
import os

os.environ["GOOGLE_CLOUD_PROJECT"] = "quoteai-firebase"


def get_db_client():
    cred = credentials.ApplicationDefault()
    firebase_admin.initialize_app(cred, {"projectId": "quoteai-firebase"})
    db_client = firestore.client()
    app = firebase_admin.get_app()
    print("Firestore Project ID:", app.project_id)
    return db_client


# gcloud auth application-default login


def get_db_client_prod():
    try:
        # Try to get the default app if it exists
        app = firebase_admin.get_app()
    except ValueError:
        # Initialize the app if it doesn't exist
        # Use absolute path to the credentials file
        cred_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "quoteai-firebase-firebase-adminsdk-5sbso-6ff415e53e.json",
        )
        cred = credentials.Certificate(cred_path)
        firebase_admin.initialize_app(
            cred, {"storageBucket": "quoteai-firebase.firebasestorage.app"}
        )

    db_client = firestore.client()
    return db_client


def get_storage_bucket():
    try:
        # Try to get the default app if it exists
        app = firebase_admin.get_app()
    except ValueError:
        # Initialize the app if it doesn't exist
        # Use absolute path to the credentials file
        cred_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "quoteai-firebase-firebase-adminsdk-5sbso-6ff415e53e.json",
        )
        cred = credentials.Certificate(cred_path)
        firebase_admin.initialize_app(
            cred, {"storageBucket": "quoteai-firebase.firebasestorage.app"}
        )

    bucket = storage.bucket()
    return bucket
