<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Simple Widget Test</title>
  
  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the built widget -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
</head>
<body>
  <h1>Simple Widget Test</h1>
  <p>This page tests the built widget directly.</p>
  
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Page loaded');
      console.log('initChatWidget available:', typeof window.initChatWidget === 'function');
      
      if (typeof window.initChatWidget === 'function') {
        window.initChatWidget({
          clientId: "test-client-simple",
          apiKey: "test-key-simple",
          uuid: "simple-" + Math.random().toString(36).substring(2, 9),
          apiUrl: "http://localhost:8080",
        });
        console.log('Widget initialized');
      } else {
        console.error('initChatWidget is not available');
      }
    });
  </script>
</body>
</html>
