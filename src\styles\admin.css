/* Modern Admin Interface Styles - Inspired by Portal Design */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

/* Global Admin Styles */
body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Admin Login Styles */
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: 'Inter', sans-serif;
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.admin-login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.8s ease-out 0.2s both;
}

@keyframes slideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.admin-login-header h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-login-header p {
  color: #6b7280;
  font-size: 1rem;
  margin: 0 0 2rem 0;
  line-height: 1.6;
  font-weight: 400;
}

.admin-login-form {
  text-align: left;
}

.form-group {
  margin-bottom: 1.5rem;
  position: relative;
}

.form-group label {
  display: block;
  color: #374151;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-group input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.form-group input:disabled {
  background-color: rgba(248, 250, 252, 0.8);
  cursor: not-allowed;
  opacity: 0.7;
}

.login-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover:not(:disabled)::before {
  left: 100%;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #dc2626;
  padding: 1rem 1.25rem;
  border-radius: 12px;
  border: 1px solid #f87171;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  text-align: center;
  font-weight: 500;
  animation: slideInFromTop 0.5s ease-out;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.admin-login-footer {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(229, 231, 235, 0.5);
}

.admin-login-footer p {
  color: #9ca3af;
  font-size: 0.75rem;
  margin: 0;
  font-weight: 400;
}

/* Customer Onboarding Form Styles */
.onboarding-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 1.5rem;
  font-family: 'Inter', sans-serif;
  animation: fadeIn 0.6s ease-out;
}

.onboarding-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  padding: 2rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.8s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.onboarding-header h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logout-button {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.logout-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.logout-button:hover::before {
  left: 100%;
}

.logout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
}

.onboarding-form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  padding: 2.5rem;
  max-width: 900px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUp 0.8s ease-out 0.3s both;
}

.form-section {
  margin-bottom: 2.5rem;
  position: relative;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 1.5rem 0;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgba(102, 126, 234, 0.2);
  position: relative;
}

.form-section-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 1px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-row.single {
  grid-template-columns: 1fr;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group textarea {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.form-group select {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-sizing: border-box;
  transition: all 0.3s ease;
  cursor: pointer;
}

.form-group select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.submit-button {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 1.25rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 2rem;
  position: relative;
  overflow: hidden;
  min-width: 200px;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submit-button:hover:not(:disabled)::before {
  left: 100%;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

.submit-button:active:not(:disabled) {
  transform: translateY(0);
}

.submit-button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.success-message {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  padding: 1.25rem 1.5rem;
  border-radius: 12px;
  border: 1px solid #34d399;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  animation: slideInFromTop 0.5s ease-out;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.success-message::before {
  content: '✓';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #22c55e;
  color: white;
  border-radius: 50%;
  font-weight: bold;
  font-size: 0.75rem;
}

.field-error {
  color: #dc2626;
  font-size: 0.75rem;
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.field-error::before {
  content: '⚠';
  color: #f59e0b;
}

/* Tag Input Styles for Specializations */
.tag-input-container {
  position: relative;
}

.tag-input-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-height: 3rem;
  align-items: center;
}

.tag-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  animation: tagSlideIn 0.3s ease-out;
}

@keyframes tagSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.tag-remove {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: all 0.2s ease;
}

.tag-remove:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.1);
}

.tag-input {
  border: none;
  outline: none;
  background: transparent;
  flex: 1;
  min-width: 120px;
  padding: 0.5rem;
  font-size: 1rem;
  font-family: inherit;
}

.tag-input::placeholder {
  color: #9ca3af;
  opacity: 1;
}

.tag-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 0.25rem;
}

.tag-suggestion {
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.tag-suggestion:last-child {
  border-bottom: none;
}

.tag-suggestion:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.tag-suggestion.highlighted {
  background: rgba(102, 126, 234, 0.15);
  color: #667eea;
}

/* Enhanced Form Input Styles */
.form-group input:not(.tag-input) {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.form-group input:not(.tag-input):focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
}

.form-group input:not(.tag-input):disabled {
  background: rgba(248, 250, 252, 0.8);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-login-card {
    padding: 2rem;
    margin: 1rem;
    border-radius: 16px;
  }

  .admin-login-header h1 {
    font-size: 1.5rem;
  }

  .onboarding-container {
    padding: 1rem;
  }

  .onboarding-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
    padding: 1.5rem;
  }

  .onboarding-header h1 {
    font-size: 1.5rem;
  }

  .onboarding-form-card {
    padding: 1.5rem;
    margin: 0;
    border-radius: 16px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .form-section-title {
    font-size: 1.125rem;
  }

  .tag-input-wrapper {
    padding: 0.625rem;
  }

  .tag {
    font-size: 0.8125rem;
    padding: 0.3125rem 0.625rem;
  }
}

@media (max-width: 480px) {
  .admin-login-card {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .admin-login-header h1 {
    font-size: 1.25rem;
  }

  .onboarding-header {
    padding: 1rem;
  }

  .onboarding-header h1 {
    font-size: 1.25rem;
  }

  .onboarding-form-card {
    padding: 1rem;
  }

  .form-section {
    margin-bottom: 2rem;
  }

  .submit-button {
    padding: 1rem 2rem;
    font-size: 1rem;
    min-width: 180px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form-group input:not(.tag-input),
  .form-group textarea,
  .form-group select {
    background: rgba(31, 41, 55, 0.8);
    color: #f9fafb;
    border-color: #4b5563;
  }

  .form-group input:not(.tag-input):focus,
  .form-group textarea:focus,
  .form-group select:focus {
    background: rgba(31, 41, 55, 0.95);
    border-color: #667eea;
  }

  .tag-input-wrapper {
    background: rgba(31, 41, 55, 0.8);
    border-color: #4b5563;
  }

  .tag-input-wrapper:focus-within {
    background: rgba(31, 41, 55, 0.95);
  }

  .tag-suggestions {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(102, 126, 234, 0.3);
  }

  .tag-suggestion {
    color: #f9fafb;
    border-color: rgba(75, 85, 99, 0.5);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form-group input:not(.tag-input),
  .form-group textarea,
  .form-group select {
    border-width: 3px;
  }

  .tag {
    border: 2px solid #000;
  }

  .submit-button,
  .login-button,
  .logout-button {
    border: 2px solid #000;
  }
}

/* Multi-Step Wizard Styles */
.multi-step-onboarding-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0.75rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  display: flex;
  flex-direction: column;
  position: relative;
}

.onboarding-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  color: white;
}

.onboarding-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.logout-button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.wizard-container {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 1.5rem);
  max-height: calc(100vh - 1.5rem);
}

/* Step Navigation Styles */
.step-navigation {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.step-progress-bar {
  position: relative;
}

.step-list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
  margin-bottom: 1rem;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.step-connector {
  position: absolute;
  top: 24px;
  left: 50%;
  right: -50%;
  height: 3px;
  background: #e2e8f0;
  z-index: 1;
  border-radius: 1.5px;
}

.step-line {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  transform-origin: left;
  border-radius: 1.5px;
  box-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

.step-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
  position: relative;
  z-index: 3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.step-circle.pending {
  background: #f1f5f9;
  color: #64748b;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.step-circle.current {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: 2px solid #667eea;
  box-shadow:
    0 0 0 4px rgba(102, 126, 234, 0.15),
    0 4px 12px rgba(102, 126, 234, 0.3);
  transform: scale(1.05);
}

.step-circle.completed {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: 2px solid #10b981;
  box-shadow:
    0 2px 8px rgba(16, 185, 129, 0.2),
    0 0 0 2px rgba(16, 185, 129, 0.1);
}

.step-circle.clickable {
  cursor: pointer;
}

.step-circle.clickable:hover {
  transform: scale(1.05);
}

.step-check {
  width: 18px;
  height: 18px;
}

.step-icon {
  font-size: 1.25rem;
  line-height: 1;
}

.step-number {
  font-size: 0.875rem;
  font-weight: 600;
}

.step-info {
  max-width: 100px;
}

.step-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  text-align: center;
  line-height: 1.2;
}

.progress-bar-container {
  position: relative;
  height: 4px;
  margin-top: 1rem;
}

.progress-bar-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background: #e2e8f0;
  border-radius: 2px;
}

.progress-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
  transition: width 0.5s ease;
}

/* Mobile Step Indicator */
.mobile-step-indicator {
  display: none;
}

/* Step Content Styles */
.step-content {
  padding: 1.5rem 2.5rem 0;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  min-height: 0; /* Allow flex item to shrink */
}

.step-header {
  text-align: center;
  margin-bottom: 1.5rem;
  flex-shrink: 0;
}

.step-header h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.step-header p {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

.step-form {
  max-width: 700px;
  margin: 0 auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex item to shrink */
  overflow-y: auto;
  padding-bottom: 1rem; /* Ensure space at bottom */
}

.form-section {
  margin-bottom: 1.5rem;
}

.form-section h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 1rem 0;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #f1f5f9;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.25rem;
  align-items: start;
}

.form-group-full {
  grid-column: 1 / -1;
}

/* Business Hours Grid */
.business-hours-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.business-hours-row {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.business-hours-row:hover {
  background: #ffffff;
  border-color: #d1d5db;
}

.day-label {
  min-width: 80px;
  font-weight: 500;
  color: #374151;
  font-size: 0.8rem;
}

.time-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.time-inputs span {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
}

.time-inputs input[type="time"] {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.75rem;
  background: #ffffff;
  flex: 1;
  min-width: 0;
}

/* Step Actions */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2.5rem;
  border-top: 1px solid #f1f5f9;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  margin-top: auto;
}

.btn-primary,
.btn-secondary {
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
  position: relative;
  overflow: hidden;
  min-height: 44px;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow:
    0 4px 12px rgba(102, 126, 234, 0.25),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow:
    0 8px 25px rgba(102, 126, 234, 0.35),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-primary:hover:not(:disabled)::before {
  opacity: 1;
}

.btn-primary:active:not(:disabled) {
  transform: translateY(-1px);
  transition: transform 0.1s ease;
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1.5px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover:not(:disabled) {
  background: #f1f5f9;
  color: #475569;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-secondary:active:not(:disabled) {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-complete {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-complete:hover:not(:disabled) {
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

/* Character Count */
.character-count {
  font-size: 0.75rem;
  color: #64748b;
  text-align: right;
  margin-top: 0.25rem;
}

/* Field Help Text */
.field-help {
  font-size: 0.75rem;
  color: #64748b;
  margin-top: 0.25rem;
  display: block;
}

/* Completion Summary */
.completion-summary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
}

.completion-summary h3 {
  color: #0c4a6e;
  margin-bottom: 0.75rem;
  border: none;
  padding: 0;
}

.completion-summary p {
  color: #0369a1;
  margin-bottom: 1.5rem;
}

.summary-checklist {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  text-align: left;
}

.checklist-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #0369a1;
}

.check-icon {
  color: #10b981;
  font-weight: bold;
}

/* Premium Form Field Styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  letter-spacing: 0.025em;
}

.form-group input:not(.tag-input),
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 0.875rem 1rem;
  background: #f8f9fa;
  border: 1.5px solid #e5e7eb;
  border-radius: 12px;
  font-size: 0.875rem;
  font-family: inherit;
  color: #1f2937;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:not(.tag-input):focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  background: #ffffff;
  border-color: #667eea;
  box-shadow:
    0 0 0 3px rgba(102, 126, 234, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.form-group input:not(.tag-input):hover:not(:focus),
.form-group textarea:hover:not(:focus),
.form-group select:hover:not(:focus) {
  border-color: #d1d5db;
  background: #ffffff;
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #ef4444;
  background: #fef2f2;
  box-shadow:
    0 0 0 3px rgba(239, 68, 68, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:disabled,
.form-group textarea:disabled,
.form-group select:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.7;
}

.field-error {
  display: block;
  color: #ef4444;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.375rem;
  line-height: 1.4;
}

.field-help {
  display: block;
  color: #6b7280;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  line-height: 1.4;
}

/* Loading Spinner */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s ease-in-out infinite;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Smooth transitions for all interactive elements */
* {
  box-sizing: border-box;
}

*:focus {
  outline: none;
}

/* Ensure smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Performance optimizations for animations */
.step-circle,
.btn-primary,
.btn-secondary,
.form-group input,
.form-group textarea,
.form-group select {
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Mobile Responsive Styles for Multi-Step Wizard */
@media (max-width: 768px) {
  .multi-step-onboarding-container {
    padding: 0.5rem;
    min-height: 100vh;
  }

  .onboarding-header {
    margin-bottom: 0.5rem;
  }

  .onboarding-header h1 {
    font-size: 1.25rem;
  }

  .wizard-container {
    border-radius: 12px;
    min-height: calc(100vh - 1rem);
    max-height: calc(100vh - 1rem);
  }

  .step-navigation {
    padding: 1rem 0.75rem;
  }

  .step-circle {
    width: 44px;
    height: 44px;
    font-size: 0.8rem;
  }

  .step-icon {
    font-size: 1.1rem;
  }

  .step-list {
    display: none;
  }

  .mobile-step-indicator {
    display: block;
    text-align: center;
  }

  .mobile-progress {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
  }

  .current-step {
    font-weight: 600;
    color: #667eea;
    font-size: 1.125rem;
  }

  .total-steps {
    color: #64748b;
    font-size: 1rem;
  }

  .mobile-progress-bar {
    height: 6px;
    background: #e2e8f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 1rem;
  }

  .mobile-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
  }

  .mobile-step-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .progress-bar-container {
    display: none;
  }

  .step-content {
    padding: 1rem 1rem 0;
    min-height: auto;
  }

  .step-form {
    padding-bottom: 0.5rem;
  }

  .step-actions {
    padding: 1rem;
  }

  .step-header {
    margin-bottom: 1rem;
  }

  .step-header h2 {
    font-size: 1.25rem;
  }

  .step-header p {
    font-size: 0.875rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .business-hours-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .business-hours-row {
    padding: 1rem;
    gap: 0.75rem;
  }

  .day-label {
    min-width: 90px;
    font-size: 0.875rem;
  }

  .time-inputs {
    gap: 0.75rem;
  }

  .time-inputs input[type="time"] {
    padding: 0.75rem;
    font-size: 0.875rem;
    min-height: 44px;
  }

  .step-actions {
    flex-direction: column;
    gap: 0.75rem;
    margin-top: auto;
    padding-top: 1rem;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .form-group input:not(.tag-input),
  .form-group textarea,
  .form-group select {
    padding: 1rem;
    font-size: 1rem;
    min-height: 48px;
  }

  .summary-checklist {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .completion-summary {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .multi-step-onboarding-container {
    padding: 0.25rem;
  }

  .step-content {
    padding: 0.75rem;
  }

  .step-header {
    margin-bottom: 0.75rem;
  }

  .step-header h2 {
    font-size: 1.125rem;
  }

  .step-header p {
    font-size: 0.8rem;
  }

  .form-section {
    margin-bottom: 1rem;
  }

  .form-section h3 {
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
  }

  .form-grid {
    gap: 0.75rem;
  }

  .business-hours-row {
    padding: 0.5rem;
    background: #f8fafc;
    border-radius: 6px;
    margin-bottom: 0.25rem;
  }

  .step-actions {
    padding-top: 0.75rem;
    gap: 0.5rem;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.75rem;
    font-size: 0.875rem;
  }
}

/* Compact styles for very small screens */
@media (max-width: 360px) {
  .onboarding-header h1 {
    font-size: 1rem;
  }

  .logout-button {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }

  .step-header h2 {
    font-size: 1rem;
  }

  .form-section h3 {
    font-size: 0.85rem;
  }
}
