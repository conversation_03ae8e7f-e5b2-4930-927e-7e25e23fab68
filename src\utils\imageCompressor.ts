/**
 * Image compression utility
 */
import { MAX_IMAGE_SIZE_BYTES } from '../constants';

export const compressImage = async (file: File, targetSizeInBytes: number = MAX_IMAGE_SIZE_BYTES, maxWidth: number = 1920): Promise<File> => {
  return new Promise((resolve, reject) => {
    if (!file.type.startsWith('image/')) {
      reject(new Error('File is not an image.'));
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      if (!event.target?.result) {
        reject(new Error('Failed to read file.'));
        return;
      }

      const img = new Image();
      img.src = event.target.result as string;

      img.onload = () => {
        const canvas = document.createElement('canvas');
        let { width, height } = img;

        // Resize if width is greater than maxWidth
        if (width > maxWidth) {
          const ratio = maxWidth / width;
          width = maxWidth;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Failed to get canvas context.'));
          return;
        }
        ctx.drawImage(img, 0, 0, width, height);

        let quality = 0.9; // Start with 90% quality for JPEG
        const compressStep = 0.1;
        const minQuality = 0.1;

        const attemptCompression = (currentQuality: number) => {
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Canvas toBlob failed.'));
                return;
              }
              
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg', // Always compress to JPEG for better size control
                lastModified: Date.now(),
              });

              if (compressedFile.size > targetSizeInBytes && currentQuality > minQuality) {
                attemptCompression(Math.max(minQuality, currentQuality - compressStep));
              } else {
                resolve(compressedFile);
              }
            },
            'image/jpeg',
            currentQuality
          );
        };
        
        // If original file is already smaller, or not a JPEG (e.g. PNG and small), return original if preferred,
        // or always compress to JPEG. For simplicity and consistent output, let's always attempt JPEG compression.
        if (file.size <= targetSizeInBytes && file.type !== 'image/gif') { // Keep small GIFs as is
             // If already small enough and not a GIF, check if it's a JPEG.
             // If not JPEG, or if it is a large JPEG, still try to compress.
             // This logic aims to compress unless it's already a small non-GIF image.
            if (file.type === 'image/jpeg' || file.size < targetSizeInBytes / 2) { // if it's a small non-jpeg or already jpeg
                // resolve(file); // Option to return original if small enough
                // return;
            }
        }
        attemptCompression(quality);
      };

      img.onerror = (err) => {
        reject(new Error(`Image loading failed: ${err}`));
      };
    };

    reader.onerror = (err) => {
      reject(new Error(`File reading failed: ${err}`));
    };
  });
};