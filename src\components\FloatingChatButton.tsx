import React from 'react';

interface FloatingChatButtonProps {
  onClick: () => void;
  unreadCount: number;
  isLoading: boolean; // For initial load or sending state if button shows this
  lastMessageTimestamp: number | null; // For re-rendering notification bubble
}

const FloatingChatButton: React.FC<FloatingChatButtonProps> = ({
  onClick,
  unreadCount,
  isLoading,
  lastMessageTimestamp,
}) => {
  return (
    <button className="chat-button" onClick={onClick} aria-label="Open chat" aria-haspopup="true">
      💬 Get a Free Quote!
      {isLoading && ( // Show loading dots if busy and chat is closed
        <span className="button-loading-indicator">
          <span className="dot"></span>
          <span className="dot"></span>
          <span className="dot"></span>
        </span>
      )}
      {unreadCount > 0 && (
        <span
          className="notification-bubble"
          key={`notification-${lastMessageTimestamp}`} // Force re-render for animation
          aria-label={`${unreadCount} new messages`}
        >
          {unreadCount}
        </span>
      )}
    </button>
  );
};

export default FloatingChatButton;