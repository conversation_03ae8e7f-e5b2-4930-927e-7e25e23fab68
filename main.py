import os
import uuid
from time import sleep
import openai
import threading
import datetime
from db import connection
from openai import OpenAI
from flask import Flask, request, jsonify, send_from_directory, redirect
from openai.types.beta import assistant
from flask_cors import CORS
from lib.open_api import OpenAPIThread
from lib.open_api import client
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from lib.gmail import send_email
from lib.api_auth import authenticate
from lib.firebase_auth import require_admin_auth
from lib.customer_validation import validate_customer_data, sanitize_customer_data, ValidationError
import json
from werkzeug.utils import secure_filename
from collections import defaultdict
import time

# ;C\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL
#TODO: REMOVE: Hardcoded API key
# ADD: Environment variables for sensitive data
# from dotenv import load_dotenv
# load_dotenv()

# API_KEY = os.getenv('API_KEY')
# FIREBASE_CREDENTIALS = os.getenv('FIREBASE_CREDENTIALS')
# OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

#TODO: REPLACE: Dev console prints with proper logging
# import logging
# from logging.handlers import RotatingFileHandler

# # Setup proper logging
# logging.basicConfig(
#     handlers=[RotatingFileHandler('app.log', maxBytes=100000, backupCount=3)],
#     level=logging.INFO,
#     format='%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
# )

# logger = logging.getLogger('quoteai')

#TODO: REPLACE: CORS handling with proper webpage URL origins
# Replace current CORS setup with more restrictive one
# CORS(app, resources={
#     r"/*": {
#         "origins": os.getenv('ALLOWED_ORIGINS', '').split(','),
#         "allow_headers": ["Content-Type", "x-api-key"],
#         "methods": ["GET", "POST", "OPTIONS"]
#     }
# })

# @app.after_request
# def add_security_headers(response):
#     response.headers['X-Content-Type-Options'] = 'nosniff'
#     response.headers['X-Frame-Options'] = 'DENY'
#     response.headers['X-XSS-Protection'] = '1; mode=block'
#     response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
#     return response

print("STARTING QUOTE AI SERVER ... ")
oat = OpenAPIThread()
fb_client = connection.get_db_client_prod()
storage_bucket = connection.get_storage_bucket()

# Thread locks for managing concurrent requests
thread_locks = {}
thread_lock_mutex = threading.Lock()

# Simple rate limiting for admin endpoints
rate_limit_storage = defaultdict(list)
RATE_LIMIT_REQUESTS = 10  # Max requests per window
RATE_LIMIT_WINDOW = 300   # 5 minutes in seconds

def check_rate_limit(ip_address: str, endpoint: str) -> bool:
    """
    Simple rate limiting check.
    Returns True if request is allowed, False if rate limited.
    """
    current_time = time.time()
    key = f"{ip_address}:{endpoint}"

    # Clean old entries
    rate_limit_storage[key] = [
        timestamp for timestamp in rate_limit_storage[key]
        if current_time - timestamp < RATE_LIMIT_WINDOW
    ]

    # Check if under limit
    if len(rate_limit_storage[key]) >= RATE_LIMIT_REQUESTS:
        return False

    # Add current request
    rate_limit_storage[key].append(current_time)
    return True

# Start the Flask App
app = Flask(__name__)
# Configure CORS with more restrictive settings for production
# TODO: Replace "*" with actual frontend domains in production
CORS(app, resources={
    r"/api/*": {
        "origins": ["http://localhost:5173", "https://quoteai-firebase.web.app"],
        "allow_headers": ["Content-Type", "Authorization"],
        "methods": ["POST", "OPTIONS"]
    },
    r"/v1/*": {
        "origins": "*",
        "allow_headers": ["Content-Type", "x-api-key"],
        "methods": ["GET", "POST", "OPTIONS"]
    },
    r"/*": {
        "origins": "*",
        "allow_headers": ["Content-Type", "x-api-key"],
        "methods": ["GET", "POST", "OPTIONS"]
    }
})

# Configure upload settings
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
MAX_CONTENT_LENGTH = 8 * 1024 * 1024  # 8MB

# Create uploads directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Add security headers
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses."""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # Add HSTS header for HTTPS (only in production)
    # response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

    return response

@app.route('/v1/ping', methods=['GET'])
def is_healthy():
    return jsonify({"status": "ok"})

# Added another health check other than ping for firebase and OpenAI
@app.route('/v1/health')
def health_check():
    try:
        # Check Firebase connection
        fb_client.collection('clients').limit(1).get()
        # Check OpenAI connection
        client.models.list()
        return jsonify({"status": "healthy"}), 200
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500

@app.route('/api/onboard', methods=['POST'])
@require_admin_auth
def onboard_customer():
    """
    Admin-only endpoint for customer onboarding.
    Validates customer data and stores it in Firestore.
    """
    # Rate limiting check
    client_ip = request.remote_addr or 'unknown'
    if not check_rate_limit(client_ip, 'onboard'):
        return jsonify({
            'error': 'Rate limit exceeded. Please try again later.',
            'code': 'RATE_LIMIT_EXCEEDED'
        }), 429

    try:
        # Get JSON data from request
        data = request.get_json()
        if not data:
            return jsonify({
                'error': 'Request body must contain JSON data',
                'code': 'INVALID_REQUEST'
            }), 400

        # Sanitize input data
        sanitized_data = sanitize_customer_data(data)

        # Validate customer data
        validation_result = validate_customer_data(sanitized_data)

        if not validation_result['valid']:
            return jsonify({
                'error': 'Validation failed',
                'code': 'VALIDATION_ERROR',
                'details': validation_result['errors']
            }), 400

        # Generate client ID and API key
        client_id = str(uuid.uuid4())
        api_key = str(uuid.uuid4())

        # Prepare customer document for Firestore
        customer_doc = {
            'client_id': client_id,
            'basic_info': {
                **sanitized_data.get('basic_info', {}),
                'client_id': client_id,
                'api_key': api_key
            },
            'contact_info': sanitized_data.get('contact_info', {}),
            'tradie_details': sanitized_data.get('tradie_details', {}),
            'ai_settings': sanitized_data.get('ai_settings', {}),
            'subscription': sanitized_data.get('subscription', {
                'plan': 'basic',
                'payment_status': 'pending'
            }),
            'metrics': {
                'total_conversations': 0,
                'total_quotes_generated': 0,
                'conversion_rate': 0.0
            },
            'timestamps': {
                'created_at': datetime.datetime.now(),
                'updated_at': datetime.datetime.now()
            }
        }

        # Store customer in Firestore
        fb_client.collection('customers').document(client_id).set(customer_doc)

        # Log successful onboarding
        print(f"Customer onboarded successfully: {client_id}")

        # Return success response
        return jsonify({
            'success': True,
            'message': 'Customer onboarded successfully',
            'client_id': client_id,
            'api_key': api_key
        }), 200

    except ValidationError as e:
        return jsonify({
            'error': str(e),
            'code': 'VALIDATION_ERROR',
            'details': e.errors if hasattr(e, 'errors') else {}
        }), 400

    except Exception as e:
        print(f"Error during customer onboarding: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'code': 'SERVER_ERROR'
        }), 500

@app.route('/initiate_conversation', methods=['OPTIONS', 'POST'])
def start():
   """
   This function is used to create the thread_id.
   Store the thread_id to Firestore.
   """
   print("\n==== INITIATE CONVERSATION ENDPOINT CALLED ====\n")

   # Handle OPTIONS request for CORS preflight
   if request.method == 'OPTIONS':
      print("Handling OPTIONS request")
      return _build_cors_preflight_response()

   print("Processing POST request")
   print(f"Headers: {request.headers}")

   # Get the website URL from the request body
   data = request.get_json()
   print(f"Request data: {data}")

   if not data:
      return jsonify({"error": "No data provided"}), 400

   # Extract website URL from request body
   website_url = None
   if 'website_url' in data:
      website_url = data['website_url']
      print(f"Using website_url from request body: {website_url}")
   # Backward compatibility
   elif 'customer_name' in data or 'uuid' in data:
      website_url = data.get('customer_name') or data.get('uuid')
      print(f"Using customer_name/uuid as website_url for backward compatibility: {website_url}")

   if not website_url:
      return jsonify({"error": "Could not determine website URL from request"}), 400

   print(f"Final website URL: {website_url}")

   # Authenticate using API key and domain
   api_key = request.headers.get("X-Api-Key")
   authentication = authenticate(api_key=api_key, domain=website_url)
   if not authentication:
      return jsonify({"Error": "You do not have permission to call this endpoint."}), 401

   # If authentication returned client data, use it
   client_data = None
   if authentication is not True:  # It's client data
      client_data = authentication

   # If we don't have client data from authentication, look it up
   if not client_data:
      clients = fb_client.collection("clients").where("website_url", "==", website_url).limit(1).get()

      for client in clients:
         client_data = client.to_dict()
         break
      
    ### TODO: Delete this for production, temp workaround for firebase related errors when trying to retrieve local data
    # If client doesn't exist, create a default entry for testing
   if not client_data:
      print(f"Client with website URL {website_url} not found, creating default entry")
      client_id = website_url.replace('.', '_')
      client_data = {
         "client_id": client_id,
         "website_url": website_url,
         "name": f"Client {website_url}",
         "email": "<EMAIL>",
         "tradie_type": "general",
         "api_key": api_key,
         "created_at": datetime.datetime.now()
      }
      # Save the default client to Firestore
      fb_client.collection("clients").document(client_id).set(client_data)
      print(f"Created default client: {client_data}")

   print(f"Client data: {client_data}")

   # Create the thread and the assistant, default to general
   tradie_type = client_data.get('tradie_type', 'general')
   print(f"Using tradie_type: {tradie_type}")

   thread_id, assistant_id = oat.create_thread_for_free_user(
      user_tradie_type=tradie_type
   )
   print(f"Created thread_id: {thread_id}, assistant_id: {assistant_id}")

   # Store the thread details in Firestore
   thread_data = {
      "assisstant_id": assistant_id,
      "thread_id": thread_id,
      "client_id": client_data.get("client_id"),
      "website_url": website_url,
      "tradie_email": client_data.get("email", "<EMAIL>"),
      "created_at": datetime.datetime.now()
   }
   fb_client.collection("thread_ids").document(thread_id).set(thread_data)
   print("Stored thread details in Firestore")

   response_data = {
      "success": "true",
      "thread_id": thread_id
   }
   print(f"Returning response: {response_data}")
   return jsonify(response_data), 200

def _build_cors_preflight_response():
    response = app.make_default_options_response()
    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, x-api-key'
    return response


def post_process(full_name, suburb, contact_number, tradie_email, summary_of_issue,preferred_time, user_email, thread_id):
   email_content = {
      "full_name": full_name,
      "phone_number": contact_number,
      "user_email": user_email,
      "suburb": suburb,
      "summary_of_issue": summary_of_issue,
      "preferred_time": preferred_time
   }

   return send_email(
      tradie_email=tradie_email,
      subject="Quote AI: Job Request.",
      email_content=email_content,
      thread_id=thread_id
   )


# For Starting the conversation
@app.route('/chat', methods=['OPTIONS', 'POST'])
def chat():
    # Step 1: Fetch the details.
    print("\n==== CHAT ENDPOINT CALLED ====\n")
    start = time.time()

    # Handle OPTIONS request for CORS preflight by returning a preflight response
    if request.method == 'OPTIONS':
        print("Handling OPTIONS request")
        return _build_cors_preflight_response()

    print(f"Headers: {request.headers}")

    # Get the data from the request
    data = request.get_json()
    print(f"Request data: {data}")

    if not data:
        return jsonify({"error": "No data provided"}), 400

    # Get thread ID and extract website URL from thread details
    thread_id = data.get('thread_id')
    if not thread_id:
        return jsonify({"error": "No thread_id provided"}), 400

    # Get thread details to extract website URL
    thread_doc = fb_client.collection("thread_ids").document(thread_id).get()
    if not thread_doc.exists:
        return jsonify({"error": "Thread not found"}), 404

    thread_details = thread_doc.to_dict()
    website_url = thread_details.get('website_url')

    # Authenticate using API key and domain
    api_key = request.headers.get("X-Api-Key")
    authentication = authenticate(api_key=api_key, domain=website_url)
    if not authentication:
        return jsonify({"Error": "You do not have permission to call this endpoint."}), 401

    # We already validated data and thread_id above

    user_input = data.get('message', '')
    if not user_input:
        return jsonify({"error": "No message provided"}), 400

    # Get attachments if any
    attachments = data.get('attachments', [])
    print(f"thread_id: {thread_id}")
    print(f"user_input: {user_input}")
    print(f"attachments: {attachments}")

    # Get or create a lock for this thread
    with thread_lock_mutex:
        if thread_id not in thread_locks:
            thread_locks[thread_id] = threading.Lock()

    # Try to acquire the lock with a timeout
    lock_acquired = thread_locks[thread_id].acquire(timeout=1)  # 1 second timeout

    if not lock_acquired:
        return jsonify({"error": "Server is busy processing another request for this thread"}), 429

    try:
        # Check if thread has an active run
        has_run, run_id = has_active_run(thread_id)
        if has_run:
            return jsonify({"error": f"Thread already has an active run (ID: {run_id}). Please wait for it to complete."}), 409

        # Get thread details
        thread_doc = fb_client.collection("thread_ids").document(thread_id).get()
        if not thread_doc.exists:
            return jsonify({"error": "Thread not found"}), 404

        thread_details = thread_doc.to_dict()

        tradie_email = thread_details.get('tradie_email')
        if not tradie_email:
            return jsonify({"error": "Tradie email not found in thread details"}), 400

        assisstant_id = thread_details.get("assisstant_id")
        if not assisstant_id:
            return jsonify({"error": "Assistant ID not found in thread details"}), 400

        # Prepare message content
        message_content = user_input

        # If there are attachments, add them to the message
        if attachments and len(attachments) > 0:
            # Format message with image links
            attachment_text = "\n\nAttached images:\n"
            for i, attachment_url in enumerate(attachments):
                attachment_text += f"[Image {i+1}]({attachment_url})\n"

            message_content += attachment_text

        # Step 2: Add a message to the thread
        client.beta.threads.messages.create(
            thread_id=thread_id,
            role="user",
            content=message_content
        )

        # Step 3: Run the Assistant (updated with wait before response)
        try:
            run = client.beta.threads.runs.create(
                thread_id=thread_id,
                assistant_id=assisstant_id
            )

            # Step 4: Check the Run Status
            # The code continuously checks the status of the assistant run.
            # It waits until the run is completed before proceeding.
            while True:
                run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
                print(f"Run status: {run.status}")

                if run.status == 'completed':
                    break
                elif run.status == "requires_action":
                    function_name = run.required_action.submit_tool_outputs.tool_calls[0].function.name
                    arguments = json.loads(run.required_action.submit_tool_outputs.tool_calls[0].function.arguments)
                    full_name = arguments.get("full_name")
                    suburb = arguments.get("suburb")
                    contact_number = arguments.get("contact_number")
                    user_email = arguments.get("email")
                    summary_of_issue = arguments.get("summary_of_issue")
                    preferred_time = arguments.get("preferred_time")
                    tool_id = run.required_action.submit_tool_outputs.tool_calls[0].id
                    
                    if function_name == 'post_process':
                        result = post_process(
                            full_name=full_name,
                            suburb=suburb,
                            summary_of_issue=summary_of_issue,
                            contact_number=contact_number,
                            tradie_email="<EMAIL>",
                            preferred_time=preferred_time,
                            user_email=user_email,
                            thread_id=thread_id
                        )
                        payload_to_gpt = {
                            "tool_call_id": tool_id,
                            "output": json.dumps(result)
                        }
                        print(payload_to_gpt)
                        client.beta.threads.runs.submit_tool_outputs_and_poll(
                            thread_id=thread_id,
                            run_id=run.id,
                            tool_outputs=[payload_to_gpt]
                        )
                        run = client.beta.threads.runs.retrieve(thread_id=thread_id, run_id=run.id)
                elif run.status in ['failed', 'cancelled', 'expired']:
                    return jsonify({"error": f"Run failed with status: {run.status}"}), 500

                # Wait a bit before checking again
                time.sleep(0.5)

            # Retrieve and return the latest message from the assistant
            messages = client.beta.threads.messages.list(thread_id=thread_id)
            response = messages.data[0].content[0].text.value
            print(f"Response: {response[:50]}...")

            end = time.time()
            print(f"Time required: {end-start:.2f} seconds")
            return jsonify({"response": response})

        except Exception as e:
            print(f"Error during OpenAI API call: {e}")
            return jsonify({"error": str(e)}), 500

    finally:
        # Always release the lock
        thread_locks[thread_id].release()


# Helper function to check if file extension is allowed
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Check if a thread has an active run
def has_active_run(thread_id: str) -> tuple[bool, str]:
    try:
        # List all runs for the thread
        runs = client.beta.threads.runs.list(thread_id=thread_id)

        # Check if any run is in progress
        for run in runs.data:
            # Access status as a property or dictionary key, depending on the OpenAI version
            run_status = getattr(run, 'status', None)
            if run_status is None and hasattr(run, 'get'):
                # Handle dictionary-like objects
                run_status = run.get('status')

            if run_status in ['queued', 'in_progress', 'requires_action']:
                # Access id as a property or dictionary key
                run_id = getattr(run, 'id', None)
                if run_id is None and hasattr(run, 'get'):
                    run_id = run.get('id')
                return True, run_id or ''

        return False, ""
    except Exception as e:
        print(f"Error checking active runs: {e}")
        return False, ""

# File upload endpoint
@app.route('/upload', methods=['POST'])
def upload_file():
    # Check if the post request has the file part
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400

    file = request.files['file']
    thread_id = request.form.get('thread_id')

    # If user does not select file, browser also
    # submit an empty part without filename
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400

    if not thread_id:
        return jsonify({'error': 'No thread_id provided'}), 400

    # Get thread details to extract website URL
    thread_doc = fb_client.collection("thread_ids").document(thread_id).get()
    if not thread_doc.exists:
        return jsonify({"error": "Thread not found"}), 404

    thread_details = thread_doc.to_dict()
    website_url = thread_details.get('website_url')

    # Authenticate using API key and domain
    api_key = request.headers.get("X-Api-Key")
    authentication = authenticate(api_key=api_key, domain=website_url)
    if not authentication:
        return jsonify({"Error": "You do not have permission to call this endpoint."}), 401

    if file and allowed_file(file.filename):
        try:
            # Generate a unique filename to prevent collisions
            original_filename = secure_filename(file.filename)
            file_extension = original_filename.rsplit('.', 1)[1].lower() if '.' in original_filename else ''
            unique_filename = f"{uuid.uuid4().hex}.{file_extension}"

            # Define the path in Firebase Storage
            storage_path = f"uploads/{thread_id}/{unique_filename}"

            # Create a blob in Firebase Storage
            blob = storage_bucket.blob(storage_path)

            # Set content type based on file extension
            content_type = None
            if file_extension == 'jpg' or file_extension == 'jpeg':
                content_type = 'image/jpeg'
            elif file_extension == 'png':
                content_type = 'image/png'
            elif file_extension == 'gif':
                content_type = 'image/gif'

            if content_type:
                blob.content_type = content_type

            # Upload the file
            blob.upload_from_file(file)

            # Make the blob publicly accessible
            # blob.make_public()

            # Get the public URL
            file_url = blob.public_url

            # Store file info in Firestore
            file_doc = {
                'original_filename': original_filename,
                'stored_filename': unique_filename,
                'storage_path': storage_path,
                'file_url': file_url,
                'thread_id': thread_id,
                'upload_time': datetime.datetime.now()
            }

            # Add to Firestore
            fb_client.collection("attachments").add(file_doc)

            return jsonify({
                'success': True,
                'file_url': file_url,
                'filename': original_filename
            })
        except Exception as e:
            print(f"Error uploading file: {str(e)}")
            return jsonify({'error': f'Error uploading file: {str(e)}'}), 500

    return jsonify({'error': 'File type not allowed'}), 400

#TODO: test if this works as it is copied from the google workspace and remove local file handling
# Redirect to Firebase Storage URL, added from firestore recommendation
@app.route('/files/<thread_id>/<filename>')
def serve_file(thread_id, filename):
    # For backward compatibility, redirect to Firebase Storage
    try:
        # Look up the file in Firestore
        attachments = fb_client.collection("attachments").where("thread_id", "==", thread_id).where("stored_filename", "==", filename).get()

        for attachment in attachments:
            file_data = attachment.to_dict()
            if 'file_url' in file_data:
                return redirect(file_data['file_url'])

        # If not found, try to construct the URL
        storage_path = f"uploads/{thread_id}/{filename}"
        blob = storage_bucket.blob(storage_path)
        if blob.exists():
            blob.make_public()
            return redirect(blob.public_url)
    except Exception as e:
        print(f"Error serving file: {str(e)}")

    # Fallback to local file if Firebase Storage fails
    return send_from_directory(os.path.join(app.config['UPLOAD_FOLDER'], thread_id), filename)

#TODO: set debug to false for prod
# Start the SERVER
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8080, debug=True)
