/**
 * Contact Information Step
 * Second step of the onboarding wizard - contact details and address
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useFormContext } from 'react-hook-form';
import TagInput from '../TagInput';
import { getErrorMessage, hasError } from '../utils/formUtils';

interface ContactInfoStepProps {
  loading: boolean;
  nextStep?: () => void;
  previousStep?: () => void;
}

const ContactInfoStep: React.FC<ContactInfoStepProps> = ({
  loading,
  nextStep,
  previousStep
}) => {
  const { register, formState: { errors }, watch, setValue, trigger } = useFormContext();
  const [showSecondaryEmails, setShowSecondaryEmails] = useState(false);

  const stepVariants = {
    initial: {
      opacity: 0,
      x: 100,
      scale: 0.95,
      rotateY: 10
    },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      rotateY: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
        mass: 0.8,
        duration: 0.6
      }
    },
    exit: {
      opacity: 0,
      x: -100,
      scale: 0.95,
      rotateY: -10,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 40,
        duration: 0.4
      }
    }
  };

  const handleNext = async () => {
    // Validate current step fields
    const isValid = await trigger([
      'contact_info.primary_email',
      'contact_info.address.street',
      'contact_info.address.suburb',
      'contact_info.address.state',
      'contact_info.address.postcode'
    ]);
    if (isValid && nextStep) {
      nextStep();
    }
  };

  const handlePrevious = () => {
    if (previousStep) {
      previousStep();
    }
  };

  const secondaryEmails = watch('contact_info.secondary_emails') || [];

  // Show secondary emails section if there are already emails
  useEffect(() => {
    if (secondaryEmails.length > 0) {
      setShowSecondaryEmails(true);
    }
  }, [secondaryEmails.length]);

  return (
    <motion.div
      className="step-content"
      variants={stepVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      style={{
        transformStyle: "preserve-3d",
        backfaceVisibility: "hidden",
        willChange: "transform, opacity"
      }}
    >
      <div className="step-header">
        <h2>Contact Information</h2>
        <p>Provide contact details and business address</p>
      </div>

      <div className="step-form">
        <div className="form-section">
          <h3>Email & Phone</h3>
          <div className="form-grid">
            <div className="form-group">
              <label htmlFor="primary_email">
                Primary Email *
              </label>
              <input
                type="email"
                id="primary_email"
                {...register('contact_info.primary_email', {
                  required: 'Primary email is required',
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: 'Please enter a valid email address'
                  }
                })}
                placeholder="<EMAIL>"
                disabled={loading}
                className={hasError(errors, 'contact_info.primary_email') ? 'error' : ''}
              />
              {hasError(errors, 'contact_info.primary_email') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'contact_info.primary_email')}
                </motion.span>
              )}
            </div>

            <div className="form-group">
              <div style={{ marginBottom: '0.75rem' }}>
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  cursor: 'pointer',
                  fontSize: '0.875rem',
                  fontWeight: '500'
                }}>
                  <input
                    type="checkbox"
                    checked={showSecondaryEmails}
                    onChange={(e) => {
                      setShowSecondaryEmails(e.target.checked);
                      // Clear secondary emails if unchecking
                      if (!e.target.checked) {
                        setValue('contact_info.secondary_emails', []);
                      }
                    }}
                    disabled={loading}
                    style={{
                      width: '16px',
                      height: '16px',
                      accentColor: '#667eea'
                    }}
                  />
                  Add Secondary Email(s)
                </label>
              </div>

              {showSecondaryEmails && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <TagInput
                    value={secondaryEmails}
                    onChange={(emails) => setValue('contact_info.secondary_emails', emails)}
                    placeholder="e.g., <EMAIL>"
                    disabled={loading}
                    maxTags={5}
                    suggestions={[]}
                  />
                </motion.div>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="phone">
                Phone
              </label>
              <input
                type="tel"
                id="phone"
                {...register('contact_info.phone')}
                placeholder="(02) 1234 5678"
                disabled={loading}
              />
            </div>

            <div className="form-group">
              <label htmlFor="mobile">
                Mobile
              </label>
              <input
                type="tel"
                id="mobile"
                {...register('contact_info.mobile')}
                placeholder="0412 345 678"
                disabled={loading}
              />
            </div>
          </div>
        </div>

        <div className="form-section">
          <h3>Business Address</h3>
          <div className="form-grid">
            <div className="form-group form-group-full">
              <label htmlFor="street">
                Street Address *
              </label>
              <input
                type="text"
                id="street"
                {...register('contact_info.address.street', {
                  required: 'Street address is required'
                })}
                placeholder="123 Main Street"
                disabled={loading}
                className={hasError(errors, 'contact_info.address.street') ? 'error' : ''}
              />
              {hasError(errors, 'contact_info.address.street') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'contact_info.address.street')}
                </motion.span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="suburb">
                Suburb *
              </label>
              <input
                type="text"
                id="suburb"
                {...register('contact_info.address.suburb', {
                  required: 'Suburb is required'
                })}
                placeholder="Sydney"
                disabled={loading}
                className={hasError(errors, 'contact_info.address.suburb') ? 'error' : ''}
              />
              {hasError(errors, 'contact_info.address.suburb') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'contact_info.address.suburb')}
                </motion.span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="state">
                State *
              </label>
              <select
                id="state"
                {...register('contact_info.address.state', {
                  required: 'State is required'
                })}
                disabled={loading}
                className={hasError(errors, 'contact_info.address.state') ? 'error' : ''}
              >
                <option value="">Select State</option>
                <option value="NSW">NSW</option>
                <option value="VIC">VIC</option>
                <option value="QLD">QLD</option>
                <option value="WA">WA</option>
                <option value="SA">SA</option>
                <option value="TAS">TAS</option>
                <option value="ACT">ACT</option>
                <option value="NT">NT</option>
              </select>
              {hasError(errors, 'contact_info.address.state') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'contact_info.address.state')}
                </motion.span>
              )}
            </div>

            <div className="form-group">
              <label htmlFor="postcode">
                Postcode *
              </label>
              <input
                type="text"
                id="postcode"
                {...register('contact_info.address.postcode', {
                  required: 'Postcode is required',
                  pattern: {
                    value: /^\d{4}$/,
                    message: 'Please enter a valid 4-digit postcode'
                  }
                })}
                placeholder="2000"
                disabled={loading}
                className={hasError(errors, 'contact_info.address.postcode') ? 'error' : ''}
              />
              {hasError(errors, 'contact_info.address.postcode') && (
                <motion.span
                  className="field-error"
                  initial={{ opacity: 0, y: -5 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {getErrorMessage(errors, 'contact_info.address.postcode')}
                </motion.span>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="step-actions">
        <button
          type="button"
          onClick={handlePrevious}
          className="btn-secondary"
          disabled={loading}
        >
          Previous
        </button>

        <button
          type="button"
          onClick={handleNext}
          className="btn-primary"
          disabled={loading}
        >
          {loading ? (
            <span className="loading-content">
              <span className="loading-spinner"></span>
              Loading...
            </span>
          ) : (
            'Next Step'
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default ContactInfoStep;
