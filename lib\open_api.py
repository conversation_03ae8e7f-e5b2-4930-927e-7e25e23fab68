# Thread Creation Here. Offcial Doc: https://platform.openai.com/docs/api-reference/assistants/createAssistant

import openai
from openai import OpenAI
from openai.types.beta import assistant
import lib.jargon as jargon

openai.api_key = "********************************************************************************************************************************************************************"

# Global
# openai.api_key = '********************************************************************************************************************************************************************'
# TODO: Store this key securely.
# TODO: This key was created using Syed's personal email. Need to create this an official quote ai open ai account and get the key.
client = OpenAI(api_key=openai.api_key)

class OpenAPIThread():
    def generate_thread_id(self):
        thread = client.beta.threads.create()
        return thread
    
    def create_thread_for_free_user(self,user_tradie_type):
        # Create the assistant
        instruction_for_bot = jargon.instructions_for_free_user(tradie_type=user_tradie_type)
        description_for_bot = jargon.get_bot_description()
        model_for_bot = jargon.gpt_model_for_free_user()
        functions = [
            {
                "name": "post_process",
                "description": "This function is used to process the all the information we have received from the user.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "full_name": {
                            "type": "string",
                            "description": "The fullname of the user.",
                        },
                        "preferred_time": {
                            "type":"string",
                            "description": "The preferred time the customer wants the job to be done."
                        },
                        "suburb":{
                            "type":"string",
                            "description": "The suburb the user is located in."
                        },
                        "contact_number":{
                            "type":"string",
                            "decription": "Mobile Number of the user."
                        },
                        "email":{
                            "type":"string",
                            "description": "The email address of the user."
                        },
                        "summary_of_issue":{
                            "type":"string",
                            "description":"The description of the issue in detail."
                        }
                    },
                    
                    "required": ["full_name","suburb","contact_number","email","summary_of_issue", "preferred_time"],
                },
            }
        ]

        assistant = client.beta.assistants.create(
            description=description_for_bot,
            instructions=instruction_for_bot,
            model=model_for_bot,
            tools=[{"type": "function", "function": functions[0]}]
        )
        thread_id = client.beta.threads.create().id
        assistant_id = assistant.id
        return thread_id, assistant_id
    
    # def post_process(full_name, suburb, contact_number, email, summary_of_issue):

    #     # Send email here.
    #     # do post process here


    #     return "Your inquiry has been sent to the plumber."
        


            # tools=[
            #     {
            #         "type": "function",
            #         "function":{
            #             "name": "post_process",
            #             "description": "Post Process the Information Received from the User.",
            #             "parameters": {
            #                 "type": "object",
            #                 "properties": {
            #                     "name": {
            #                         "type": "string",
            #                         "description": "The name of the user",
            #                     }
            #                 },
            #                 "required": ["name"],
            #             },
            #         }
            #     }
            # ]

# openai.api_key = '********************************************************************************************************************************************************************'
# client = OpenAI(api_key=openai.api_key)

# vector_store = client.beta.vector_stores.create(name="Electrian Q/A")

# file_path = ['sparkie_knowledge_space (2).docx']
# file_streams = [open(path, "rb") for path in file_path]

# file_batch = client.beta.vector_stores.file_batches.upload_and_poll(
#   vector_store_id=vector_store.id, files=file_streams
# )

# print(file_batch.status)
# print(file_batch.file_counts)


# quote_assistants = client.beta.assistants.create(
#   name="QuoteAI",
#   description="Interactive chabot designed to provide users with information on quotes. The bot is helpful, creative, clever, and very friendly.",
#   model="gpt-3.5-turbo",
#   tools=[{"type": "file_search"}],
#   tool_resources={"file_search": {"vector_store_ids": [vector_store.id]}}
# )

# thread = client.beta.threads.create()



    # def create_assistant(self):
    #     # HARDCODED.
    #     instructions = jargon.get_assisstant_instructions()
    #     vector_store = client.beta.vector_stores.create(name="Plumbing Q/A")
    #     file_path = ['/Users/<USER>/Desktop/QuoteAI/code/quoteai/server/lib/knowledge_base/Ikrams_Plumbing_KB.pdf']
    #     file_streams = [open(path,"rb") for path in file_path]
    #     file_batch = client.beta.vector_stores.file_batches.upload_and_poll(
    #         vector_store_id=vector_store.id, files=file_streams
    #     )
    #     quote_assistants = client.beta.assistants.create(
    #         name="QuoteAI",
    #         description="Interactive chabot designed to provide users with information on quotes. The bot is helpful, creative, clever, and very friendly.",
    #         instructions=instructions
    #         model="gpt-3.5-turbo-0125",
    #         tools=[{"type": "file_search"}],
    #         tool_resources={"file_search": {"vector_store_ids": [vector_store.id]}}
    #     )
    #     return quote_assistants