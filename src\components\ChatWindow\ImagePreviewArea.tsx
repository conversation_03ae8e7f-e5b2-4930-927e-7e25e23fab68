import React from 'react';
import type { ImagePreview as ImagePreviewType } from '../../types';
import { MAX_IMAGES_UPLOAD } from '../../constants';

interface ImagePreviewAreaProps {
  previewImageObjects: ImagePreviewType[];
  onRemoveImage: (index: number) => void;
  onClearAllImages: () => void;
  onAddImageClick: () => void; // For the "Add Image" box
  onImageClick: (url: string) => void; // For expanding image
  imageError?: string | null;
  isUploading?: boolean;
}

const ImagePreviewArea: React.FC<ImagePreviewAreaProps> = ({
  previewImageObjects,
  onRemoveImage,
  onClearAllImages,
  onAddImageClick,
  onImageClick,
  imageError,
  isUploading
}) => {
  if (previewImageObjects.length === 0 && !imageError) return null;

  return (
    <div className="image-preview-container">
      <div className="image-preview-header">
        <span className="image-count">
          {previewImageObjects.length} of {MAX_IMAGES_UPLOAD} images
          {isUploading && " (Uploading...)"}
        </span>
        {previewImageObjects.length > 0 && (
          <button
            className="clear-all-btn"
            onClick={onClearAllImages}
            disabled={isUploading}
            aria-label="Clear all selected images"
          >
            Clear All
          </button>
        )}
      </div>
       {imageError && <div role="alert" style={{color: 'red', fontSize: '12px', padding: '0 0 8px 0'}}>{imageError}</div>}
      <div className="image-grid">
        {previewImageObjects.map((preview, index) => (
          <div key={preview.url} className="image-preview">
            <img
              src={preview.url}
              alt={`Preview ${index + 1}`}
              onClick={() => onImageClick(preview.url)}
              tabIndex={0}
              onKeyDown={(e) => e.key === 'Enter' && onImageClick(preview.url)}
            />
            <button
              className="remove-image-btn"
              onClick={() => onRemoveImage(index)}
              aria-label={`Remove image ${index + 1}`}
              title={`Remove image ${index + 1}`}
              disabled={isUploading}
            >
              ×
            </button>
          </div>
        ))}
        {previewImageObjects.length < MAX_IMAGES_UPLOAD && (
          <button
            className="add-image-box"
            onClick={onAddImageClick}
            aria-label="Add more images"
            title="Add more images"
            disabled={isUploading}
          >
            <span className="add-icon" aria-hidden="true">+</span>
            <span className="add-text">Add Image</span>
          </button>
        )}
      </div>
    </div>
  );
};

export default ImagePreviewArea;