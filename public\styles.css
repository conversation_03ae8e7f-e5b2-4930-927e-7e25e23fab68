/* General Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  body {
    font-family: 'Open Sans', sans-serif;
    background-color: #f4f4f4;
    color: #333;
  }
  
  a {
    text-decoration: none;
    color: inherit;
  }
  
  /* Header Styles */
  header {
    background-color: #1b8ae4;
    padding: 20px 0;
    color: white;
  }
  
  header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  header .logo h1 {
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 2rem;
  }
  
  header .logo p {
    font-size: 1rem;
    margin-top: 5px;
  }
  
  .logo {
    padding: 0 2%;
  }

  nav {
    padding: 0 2%;
  }

  nav ul {
    display: flex;
    gap: 20px;
  }
  
  nav ul li {
    list-style: none;
  }
  
  nav ul li a {
    font-weight: 700;
    color: white;
    font-size: 1.2rem;
  }
  
  nav ul li a:hover {
    color: #53a2be;
  }
  
  /* Hero Section */
  #hero {
    background-color: #132e32;
    color: white;
    padding: 80px 20px;
    text-align: center;
  }
  
  #hero h2 {
    font-size: 2.5rem;
    margin-bottom: 10px;
  }
  
  #hero p {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }
  
  .cta-button {
    background-color: #1b8ae4;
    padding: 15px 25px;
    font-size: 1.2rem;
    color: white;
    border-radius: 5px;
    transition: background-color 0.3s;
  }
  
  .cta-button:hover {
    background-color: #53a2be;
  }
  
  /* Services Section */
  #services {
    padding: 60px 20px;
    background-color: #fff;
    text-align: center;
  }
  
  #services h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
  
  .service-list {
    display: flex;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    justify-content: center;
    gap: 20px;
  }
  
  .service-item {
    background-color: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }
  
  .service-item h3 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #1b8ae4;
  }
  
  .service-item p {
    font-size: 1rem;
    color: #555;
  }
  
  /* About Section */
  #about {
    padding: 60px 20px;
    background-color: #f4f4f4;
    text-align: center;
  }
  
  #about h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
  
  /* Contact Section */
  #contact {
    padding: 60px 20px;
    background-color: #fff;
    text-align: center;
  }
  
  #contact h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
  }
  
  form {
    max-width: 600px;
    margin: 0 auto;
    text-align: left;
  }
  
  .form-group {
    margin-bottom: 20px;
  }
  
  .form-group label {
    display: block;
    font-weight: 700;
    margin-bottom: 5px;
  }
  
  .form-group input,
  .form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
  }
  
  button.cta-button {
    width: 100%;
    padding: 15px 0;
    background-color: #1b8ae4;
    color: white;
    border-radius: 5px;
    border: none;
    font-size: 1.2rem;
  }
  
  button.cta-button:hover {
    background-color: #53a2be;
  }
  
  /* Footer Styles */
  footer {
    background-color: #132e32;
    color: white;
    padding: 20px 0;
    text-align: center;
  }
  
  /* Mobile styles */
@media (max-width: 768px) {
    .container {
      padding: 0 10px;
    }
  
    .service-list {
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }
  
    .service-item {
      width: 100%;  /* Allow items to take full width on mobile */
      text-align: center;
    }
  
    nav ul {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  
    nav ul li {
      margin: 10px 0;
    }
  
    .hero-content h2 {
      font-size: 1.5rem;
    }
  
    .cta-button {
      padding: 10px 20px;
      font-size: 1rem;
    }
  }