/**
 * Admin interface entry point
 */

import { createRoot } from 'react-dom/client';
import AdminApp from './AdminApp';

console.info('🔐 Admin interface loading...');

// Initialize admin interface
const initAdminInterface = () => {
  let container = document.getElementById('admin-root');
  if (!container) {
    container = document.createElement('div');
    container.id = 'admin-root';
    document.body.appendChild(container);
  }

  console.info('🔐 Admin container found:', container);
  console.info('🧩 Creating React root for admin...');

  try {
    const root = createRoot(container);
    console.info('⚛️ React root created, rendering AdminApp...');

    root.render(<AdminApp />);

    console.info('🎉 AdminApp rendered successfully');
  } catch (error) {
    console.error('❌ Failed to render AdminApp:', error);
  }
};

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initAdminInterface);
} else {
  initAdminInterface();
}

// Export for manual initialization if needed
export { initAdminInterface };
