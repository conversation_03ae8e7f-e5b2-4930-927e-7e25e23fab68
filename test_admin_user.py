#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create an admin user for testing the customer onboarding system.
This should only be run once to set up the initial admin user.
"""

import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.firebase_auth import create_admin_user
from db.connection import get_db_client_prod

def main():
    """Create an admin user for testing."""

    print("Creating admin user for QuoteAI Customer Onboarding System")
    print("=" * 60)

    try:
        # Initialize Firebase connection first
        print("🔥 Initializing Firebase connection...")
        db_client = get_db_client_prod()
        print("✅ Firebase initialized successfully")

    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {str(e)}")
        print("Please ensure Firebase credentials are properly configured.")
        return

    # Get admin credentials
    email = input("\nEnter admin email: ").strip()
    if not email:
        print("Error: Email is required")
        return

    password = input("Enter admin password (min 6 characters): ").strip()
    if len(password) < 6:
        print("Error: Password must be at least 6 characters")
        return

    try:
        # Create the admin user
        print(f"\n👤 Creating admin user: {email}")
        uid = create_admin_user(email, password)

        print(f"\n✅ Admin user created successfully!")
        print(f"Email: {email}")
        print(f"UID: {uid}")
        print(f"Admin privileges: Enabled")
        print("\nYou can now use these credentials to log into the admin interface.")

    except Exception as e:
        print(f"\n❌ Error creating admin user: {str(e)}")
        return

if __name__ == "__main__":
    main()
