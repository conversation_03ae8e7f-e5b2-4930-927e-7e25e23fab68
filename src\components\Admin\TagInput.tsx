/**
 * Tag Input Component for Specializations
 * Provides an intuitive tag-style input with suggestions
 */

import React, { useState, useRef, useEffect } from 'react';

interface TagInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  suggestions?: string[];
  disabled?: boolean;
  maxTags?: number;
}

const defaultSuggestions = [
  'Bathroom renovation',
  'Kitchen plumbing',
  'Blocked drains',
  'Tap repairs',
  'Toilet repairs',
  'Hot water systems',
  'Gas fitting',
  'Pipe repairs',
  'Leak detection',
  'Emergency plumbing',
  'Commercial plumbing',
  'Residential plumbing',
  'Water heater installation',
  'Sewer line repair',
  'Fixture installation',
  'Pipe relining',
  'Backflow prevention',
  'Water filtration',
  'Grease trap cleaning',
  'Storm water drainage'
];

const TagInput: React.FC<TagInputProps> = ({
  value = [],
  onChange,
  placeholder = 'Type and press Enter or Tab to add...',
  suggestions = defaultSuggestions,
  disabled = false,
  maxTags = 10
}) => {
  const [inputValue, setInputValue] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Filter suggestions based on input and exclude already selected tags
  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(inputValue.toLowerCase()) &&
    !value.includes(suggestion)
  );

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !value.includes(trimmedTag) && value.length < maxTags) {
      onChange([...value, trimmedTag]);
      setInputValue('');
      setShowSuggestions(false);
      setHighlightedIndex(-1);
    }
  };

  const removeTag = (indexToRemove: number) => {
    onChange(value.filter((_, index) => index !== indexToRemove));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    setShowSuggestions(newValue.length > 0);
    setHighlightedIndex(-1);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      if (highlightedIndex >= 0 && highlightedIndex < filteredSuggestions.length) {
        addTag(filteredSuggestions[highlightedIndex]);
      } else if (inputValue.trim()) {
        addTag(inputValue);
      }
    } else if (e.key === 'Tab') {
      // Allow Tab to add tag if there's input, but don't prevent default tab behavior
      if (inputValue.trim()) {
        e.preventDefault();
        addTag(inputValue);
      }
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setHighlightedIndex(prev =>
        prev < filteredSuggestions.length - 1 ? prev + 1 : 0
      );
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setHighlightedIndex(prev =>
        prev > 0 ? prev - 1 : filteredSuggestions.length - 1
      );
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
      setHighlightedIndex(-1);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      removeTag(value.length - 1);
    }
    // Note: Removed comma handling to allow commas within tags
  };

  const handleSuggestionClick = (suggestion: string) => {
    addTag(suggestion);
    inputRef.current?.focus();
  };

  const handleContainerClick = () => {
    inputRef.current?.focus();
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="tag-input-container" ref={containerRef}>
      <div 
        className="tag-input-wrapper" 
        onClick={handleContainerClick}
        style={{ opacity: disabled ? 0.6 : 1 }}
      >
        {value.map((tag, index) => (
          <span key={index} className="tag">
            {tag}
            {!disabled && (
              <button
                type="button"
                className="tag-remove"
                onClick={(e) => {
                  e.stopPropagation();
                  removeTag(index);
                }}
                aria-label={`Remove ${tag}`}
              >
                ×
              </button>
            )}
          </span>
        ))}
        
        {!disabled && value.length < maxTags && (
          <input
            ref={inputRef}
            type="text"
            className="tag-input"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleInputKeyDown}
            onFocus={() => inputValue && setShowSuggestions(true)}
            placeholder={value.length === 0 ? placeholder : ''}
            disabled={disabled}
          />
        )}
      </div>

      {showSuggestions && filteredSuggestions.length > 0 && !disabled && (
        <div className="tag-suggestions">
          {filteredSuggestions.slice(0, 8).map((suggestion, index) => (
            <div
              key={suggestion}
              className={`tag-suggestion ${index === highlightedIndex ? 'highlighted' : ''}`}
              onClick={() => handleSuggestionClick(suggestion)}
              onMouseEnter={() => setHighlightedIndex(index)}
            >
              {suggestion}
            </div>
          ))}
        </div>
      )}
      
      {/* Helper text */}
      <div style={{
        fontSize: '0.75rem',
        color: '#6b7280',
        marginTop: '0.5rem',
        fontStyle: 'italic'
      }}>
        {value.length >= maxTags ? (
          `Maximum ${maxTags} items allowed`
        ) : (
          'Press Enter or Tab to add. Commas are allowed within items.'
        )}
      </div>
    </div>
  );
};

export default TagInput;
