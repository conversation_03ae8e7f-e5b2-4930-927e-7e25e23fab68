import React, { useEffect, useRef } from 'react';
import type { Message, Suggestion } from '../../types';
import MessageItem from './MessageItem';
import ScrollToBottomButton from './ScrollToBottomButton';

interface MessageListProps {
  messages: Message[];
  isLoading: boolean; // For bot typing indicator
  queueLength: number;
  onSuggestionClick: (suggestion: Suggestion) => void;
  onImageClick: (url: string) => void;
  isPortalMode?: boolean;
}

const MessageList: React.FC<MessageListProps> = ({
  messages,
  isLoading,
  queueLength,
  onSuggestionClick,
  onImageClick,
  isPortalMode = false,
}) => {
  const messagesContainerRef = useRef<HTMLDivElement | null>(null);
  const chatEndRef = useRef<HTMLDivElement | null>(null);
  const shouldScrollRef = useRef<boolean>(true); // Track if we should auto-scroll

  // Function to check if user is near bottom
  const isNearBottom = (container: HTMLDivElement, threshold = 100): boolean => {
    const { scrollTop, scrollHeight, clientHeight } = container;
    return scrollHeight - scrollTop - clientHeight < threshold;
  };

  // Function to scroll to bottom
  const scrollToBottom = (container: HTMLDivElement, smooth = true): void => {
    const scrollOptions: ScrollToOptions = {
      top: container.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto'
    };
    container.scrollTo(scrollOptions);
  };

  // Track scroll position to determine if user is manually scrolling
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      // Update shouldScroll based on current position
      shouldScrollRef.current = isNearBottom(container);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, []);

  // Auto-scroll when messages change
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) {
      console.log('🔍 MessageList: No container found');
      return;
    }

    // Always scroll for first message or if user was near bottom
    const shouldAutoScroll = messages.length === 1 || shouldScrollRef.current;

    console.log('🔍 MessageList: Scroll check', {
      messagesLength: messages.length,
      isLoading,
      shouldScrollRef: shouldScrollRef.current,
      shouldAutoScroll,
      isPortalMode,
      scrollHeight: container.scrollHeight,
      clientHeight: container.clientHeight
    });

    if (shouldAutoScroll) {
      console.log('🔄 MessageList: Auto-scrolling...');
      // Use requestAnimationFrame to ensure DOM is updated
      requestAnimationFrame(() => {
        if (container && container.parentNode) {
          scrollToBottom(container);
          // Update the scroll tracking after scrolling
          shouldScrollRef.current = true;
          console.log('✅ MessageList: Scrolled to', container.scrollTop, '/', container.scrollHeight);
        }
      });
    } else {
      console.log('🚫 MessageList: Not auto-scrolling (user not at bottom)');
    }
  }, [messages, isLoading]); // Trigger on messages or loading state changes

  return (
    <>
      <div
        className={`chat-messages ${isPortalMode ? 'portal-mode' : ''}`}
        aria-live="polite"
        aria-atomic="false"
        ref={messagesContainerRef}
      >
        {messages.map((msg, index) => (
          <MessageItem
            key={`${msg.type}-${msg.timestamp}-${index}`} // More robust key
            message={msg}
            onSuggestionClick={onSuggestionClick}
            onImageClick={onImageClick}
          />
        ))}
        {isLoading && (
          <div className="loading-dots" aria-label="Bot is typing">
            <span>.</span>
            <span>.</span>
            <span>.</span>
          </div>
        )}
        {/* Queue status indicator - consider if this is part of messages or separate UI */}
        {queueLength > 0 && !isLoading && ( // Show only if not already showing main loading
            <div className="queue-indicator">
                {queueLength} message{queueLength > 1 ? 's' : ''} in queue
            </div>
        )}
        {/* Invisible element at the end for scroll reference */}
        <div ref={chatEndRef} style={{ height: '1px', visibility: 'hidden' }} aria-hidden="true" />
      </div>
      <ScrollToBottomButton messagesContainerRef={messagesContainerRef} isPortalMode={isPortalMode} />
    </>
  );
};

export default MessageList;