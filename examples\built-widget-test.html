<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Built Widget Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      line-height: 1.6;
    }
    .debug-panel {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .debug-log {
      background: #000;
      color: #0f0;
      padding: 10px;
      border-radius: 5px;
      font-family: monospace;
      height: 200px;
      overflow-y: auto;
      margin-top: 10px;
    }
    button {
      background: #1b8ae4;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    button:hover {
      background: #0c6cb3;
    }
  </style>
  
  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the built widget -->
  <script src="/chatbot-widget.umd.js"></script>
</head>
<body>
  <h1>Built Widget Test</h1>
  
  <div class="debug-panel">
    <h2>Debug Controls</h2>
    <button id="checkInitFunction">Check initChatWidget</button>
    <button id="manualInit">Manual Initialize</button>
    <button id="clearStorage">Clear Storage</button>
    <button id="clearLog">Clear Log</button>
    
    <div class="debug-log" id="debugLog"></div>
  </div>

  <script>
    // Debug logging function
    function log(message) {
      const debugLog = document.getElementById('debugLog');
      const timestamp = new Date().toLocaleTimeString();
      debugLog.innerHTML += `<div>[${timestamp}] ${message}</div>`;
      debugLog.scrollTop = debugLog.scrollHeight;
    }
    
    // Check if a variable exists and log its type
    function checkVar(name, variable) {
      if (typeof variable === 'undefined') {
        log(`❌ ${name} is undefined`);
      } else {
        log(`✅ ${name} is defined (type: ${typeof variable})`);
        if (typeof variable === 'function') {
          log(`   Function details: ${variable.toString().substring(0, 100)}...`);
        } else if (typeof variable === 'object') {
          try {
            log(`   Object keys: ${Object.keys(variable).join(', ')}`);
          } catch (e) {
            log(`   Cannot enumerate object keys: ${e.message}`);
          }
        }
      }
    }
    
    // Set up event listeners
    document.addEventListener('DOMContentLoaded', () => {
      log('Page loaded');
      
      // Check if React and ReactDOM are loaded
      checkVar('React', window.React);
      checkVar('ReactDOM', window.ReactDOM);
      checkVar('initChatWidget', window.initChatWidget);
      
      // Button event listeners
      document.getElementById('checkInitFunction').addEventListener('click', () => {
        checkVar('initChatWidget', window.initChatWidget);
      });
      
      document.getElementById('manualInit').addEventListener('click', () => {
        try {
          log('Attempting manual initialization...');
          if (typeof window.initChatWidget === 'function') {
            window.initChatWidget({
              clientId: "test-client-debug",
              apiKey: "test-key-debug",
              uuid: "debug-" + Math.random().toString(36).substring(2, 9),
              apiUrl: "http://localhost:8080",
            });
            log('Manual initialization called successfully');
          } else {
            log('Cannot initialize: initChatWidget is not a function');
          }
        } catch (e) {
          log(`Error during manual init: ${e.message}`);
        }
      });
      
      document.getElementById('clearStorage').addEventListener('click', () => {
        localStorage.clear();
        log('localStorage cleared');
      });
      
      document.getElementById('clearLog').addEventListener('click', () => {
        document.getElementById('debugLog').innerHTML = '';
        log('Log cleared');
      });
    });
  </script>
</body>
</html>
