<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Widget - Minimal Example</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Widget - Minimal Example</h1>
    <p>This is a minimal example of the QuoteAI widget integration.</p>
  </div>

  <!-- 1. Load React and ReactDOM (required dependencies) -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.production.min.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.production.min.js"></script>
  
  <!-- 2. Load the widget script and styles -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">
  
  <!-- 3. Initialize the widget -->
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      if (typeof window.initChatWidget === 'function') {
        window.initChatWidget({
          apiKey: ";C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
          websiteUrl: "example.com",
          apiUrl: "http://localhost:8080"
        });
        console.log('Widget initialized successfully!');
      } else {
        console.error('Widget initialization failed: initChatWidget function not available');
      }
    });
  </script>
</body>
</html>
