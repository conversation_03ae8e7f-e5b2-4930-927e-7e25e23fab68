<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Chat Persistence Test</title>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@600&family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
  <style>
    body {
      font-family: 'Open Sans', sans-serif;
      margin: 0;
      padding: 0;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    header {
      background-color: #1b8ae4;
      color: white;
      padding: 20px 0;
    }
    .logo h1 {
      font-family: 'Montserrat', sans-serif;
      margin: 0;
      font-size: 28px;
    }
    .logo p {
      margin: 5px 0 0;
      font-size: 14px;
    }
    .test-controls {
      background-color: #f5f5f5;
      padding: 20px;
      margin: 20px 0;
      border-radius: 5px;
    }
    .test-controls h2 {
      margin-top: 0;
      color: #1b8ae4;
    }
    button {
      background-color: #1b8ae4;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 5px;
      cursor: pointer;
      margin-right: 10px;
      font-family: 'Open Sans', sans-serif;
    }
    button:hover {
      background-color: #0c6cb3;
    }
    .storage-info {
      margin-top: 20px;
      padding: 15px;
      background-color: #fff;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .storage-info h3 {
      margin-top: 0;
      color: #1b8ae4;
    }
    .storage-info ul {
      padding-left: 20px;
    }
    .storage-info li {
      margin-bottom: 10px;
    }
    .instructions {
      background-color: #fffde7;
      padding: 15px;
      border-radius: 5px;
      margin: 20px 0;
      border-left: 4px solid #ffd600;
    }
  </style>
</head>

<body>
  <header>
    <div class="container">
      <div class="logo">
        <h1>QuoteAI Chat Persistence Test</h1>
        <p>Testing chat session persistence with localStorage</p>
      </div>
    </div>
  </header>

  <div class="container">
    <div class="instructions">
      <h3>Instructions</h3>
      <p>This page helps you test the chat persistence functionality. Follow these steps:</p>
      <ol>
        <li>Open the chat widget by clicking the "Get a Free Quote!" button</li>
        <li>Send a few messages and upload an image</li>
        <li>Click the "Simulate Page Refresh" button below</li>
        <li>Verify that your chat history and images are still visible</li>
        <li>You can also close this tab and reopen it to test real persistence</li>
      </ol>
    </div>

    <div class="test-controls">
      <h2>Chat Persistence Test Controls</h2>
      <button id="refreshButton">Simulate Page Refresh</button>
      <button id="clearStorageButton">Clear All Storage</button>
      <div class="storage-info" id="storageInfo"></div>
    </div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>

  <!-- Load chatbot widget -->
  <script type="module" src="/src/main.tsx"></script>

  <script>
    // Function to display localStorage info
    function displayStorageInfo() {
      const storageInfo = document.getElementById('storageInfo');
      const items = { ...localStorage };
      
      let html = '<h3>Current localStorage Contents:</h3>';
      
      if (Object.keys(items).length === 0) {
        html += '<p>No items in localStorage</p>';
      } else {
        html += '<ul>';
        for (const key in items) {
          if (key.startsWith('quoteai_')) {
            try {
              const value = JSON.parse(items[key]);
              const messageCount = value.messages ? value.messages.length : 0;
              const expiryDate = value.expiresAt ? new Date(value.expiresAt).toLocaleString() : 'N/A';
              
              html += `<li><strong>${key}</strong>: 
                      <br>- Messages: ${messageCount}
                      <br>- Thread ID: ${value.threadId || 'None'}
                      <br>- Expires: ${expiryDate}
                      </li>`;
            } catch (e) {
              html += `<li><strong>${key}</strong>: ${items[key]}</li>`;
            }
          } else {
            html += `<li><strong>${key}</strong>: ${items[key]}</li>`;
          }
        }
        html += '</ul>';
      }
      
      storageInfo.innerHTML = html;
    }
    
    // Set up event listeners when DOM is ready
    document.addEventListener('DOMContentLoaded', (event) => {
      // Initialize widget with a delay to ensure it's loaded
      setTimeout(() => {
        if (typeof window.initChatWidget === 'function') {
          const clientUUID = "test-client-" + Math.random().toString(36).substring(2, 15);
          window.initChatWidget({
            clientId: "test-client-local",
            apiKey: "test-key-local",
            uuid: clientUUID,
            apiUrl: "http://localhost:8080",
          });
          console.log('Chat widget initialized');
        } else {
          console.error('initChatWidget is not available');
        }
      }, 500);
      
      // Display localStorage info
      displayStorageInfo();
      
      // Simulate page refresh
      document.getElementById('refreshButton').addEventListener('click', function() {
        window.location.reload();
      });
      
      // Clear all storage
      document.getElementById('clearStorageButton').addEventListener('click', function() {
        localStorage.clear();
        displayStorageInfo();
        alert('All localStorage data cleared. Refresh the page to start fresh.');
      });
      
      // Update storage info every 2 seconds
      setInterval(displayStorageInfo, 2000);
    });
  </script>
</body>

</html>
