# QuoteAI Portal URL Rewriting
# Handles URLs like /chat/tradie-name/ and redirects to appropriate portal

RewriteEngine On

# Security: Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Prevent direct access to portal.html
RewriteCond %{THE_REQUEST} \s/+chat/portal\.html[\s?] [NC]
RewriteRule ^ - [F]

# Handle tradie-specific URLs
# /chat/demo -> /chat/demo/index.html
# /chat/tradie-name -> /chat/portal.html with tradie parameter

# Special case for demo - redirect to demo directory
RewriteCond %{REQUEST_URI} ^/chat/demo/?$
RewriteRule ^demo/?$ demo/index.html [L]

# For all other tradie names, use the generic portal
RewriteCond %{REQUEST_URI} ^/chat/([^/]+)/?$
RewriteCond %1 !^(demo|assets)$
RewriteRule ^([^/]+)/?$ ../portal.html [L]

# Fallback: if someone accesses /chat/ without a tradie name, redirect to demo
RewriteCond %{REQUEST_URI} ^/chat/?$
RewriteRule ^$ demo/index.html [R=302,L]

# Handle missing files gracefully
ErrorDocument 404 /chat/demo/index.html

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>
