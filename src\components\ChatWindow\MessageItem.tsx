import React from 'react';
import type { Message, Suggestion } from '../../types';

interface MessageItemProps {
  message: Message;
  onSuggestionClick: (suggestion: Suggestion) => void;
  onImageClick: (url: string) => void;
}

const MessageItem: React.FC<MessageItemProps> = ({ message, onSuggestionClick, onImageClick }) => {
  const timestamp = message.timestamp ? new Date(message.timestamp) : new Date();
  const formattedTime = timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

  return (
    <div className={`message-container ${message.type}-container`}>
      <div className={`message ${message.type}`}>
        <div className="message-content">
          {message.content && <div className="message-text" dangerouslySetInnerHTML={{ __html: message.content.replace(/\n/g, '<br />') }} />}
          {message.attachments && message.attachments.length > 0 && (
            <div className="message-attachments">
              {message.attachments.map((url, i) => (
                <div key={`${message.timestamp}-att-${i}`} className="message-attachment-container">
                  <img
                    src={url}
                    alt={`Attachment ${i + 1}`}
                    className="message-attachment-img"
                    onClick={() => onImageClick(url)}
                    tabIndex={0} // Make it focusable
                    onKeyDown={(e) => e.key === 'Enter' && onImageClick(url)}
                  />
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      <div className={`message-timestamp ${message.type}`}>{formattedTime}</div>
      {message.type === 'bot' && message.suggestions && message.suggestions.length > 0 && (
        <div className="suggestions-container">
          {message.suggestions.map((suggestion, idx) => (
            <button
              key={`${message.timestamp}-sug-${idx}`}
              className="suggestion-bubble"
              onClick={() => onSuggestionClick(suggestion)}
            >
              {suggestion.text}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default MessageItem;