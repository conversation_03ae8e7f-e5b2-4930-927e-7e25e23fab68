import { useState, useEffect, useCallback } from 'react';
import { playNotificationSound } from '../utils/notificationSound';
import type { Message } from '../types';

export interface UseChatVisibilityReturn {
  isChatOpen: boolean;
  unreadCount: number;
  toggleChat: () => void;
  handleNewBotMessage: (lastMessage?: Message) => void;
  resetUnreadCount: () => void;
  lastMessageTimestamp: number | null;
}

export const useChatVisibility = (initialOpenState = false): UseChatVisibilityReturn => {
  const [isChatOpen, setIsChatOpen] = useState(initialOpenState);
  const [unreadCount, setUnreadCount] = useState(0);
  const [lastMessageTimestamp, setLastMessageTimestamp] = useState<number | null>(null);


  const toggleChat = useCallback(() => {
    setIsChatOpen(prev => {
      if (!prev) { // Opening the chat
        setUnreadCount(0);
      }
      return !prev;
    });
  }, []);

  const resetUnreadCount = useCallback(() => {
    setUnreadCount(0);
  }, []);

  const handleNewBotMessage = useCallback((lastMessage?: Message) => {
    if (lastMessage && lastMessage.type === 'bot') {
        setLastMessageTimestamp(lastMessage.timestamp || Date.now());
        if (!isChatOpen) {
            setUnreadCount(prev => prev + 1);
            playNotificationSound();
        }
    }
  }, [isChatOpen]);

  // Effect to reset unread count if chat is opened by other means (e.g. external event)
  useEffect(() => {
    if (isChatOpen) {
      setUnreadCount(0);
    }
  }, [isChatOpen]);

  return {
    isChatOpen,
    unreadCount,
    toggleChat,
    handleNewBotMessage,
    resetUnreadCount,
    lastMessageTimestamp,
  };
};