<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuoteAI Simple Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
    }
    .log-container {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f8f8;
      border: 1px solid #ddd;
      border-radius: 4px;
      max-height: 200px;
      overflow-y: auto;
    }
    .log-entry {
      margin: 5px 0;
      font-family: monospace;
    }
    .success {
      color: green;
    }
    .error {
      color: red;
    }
    .info {
      color: blue;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>QuoteAI Simple Test</h1>
    
    <div id="log" class="log-container"></div>
  </div>

  <!-- Load React and ReactDOM from CDN -->
  <script src="https://unpkg.com/react@18.0.0/umd/react.development.js"></script>
  <script src="https://unpkg.com/react-dom@18.0.0/umd/react-dom.development.js"></script>
  
  <!-- Load the ChatWidget component -->
  <script src="/dist/chatbot-widget.umd.js"></script>
  <link rel="stylesheet" href="/dist/style.css">

  <script>
    // Helper function to log messages
    function log(message, type = 'info') {
      const container = document.getElementById('log');
      const entry = document.createElement('div');
      entry.className = `log-entry ${type}`;
      entry.textContent = message;
      container.appendChild(entry);
      container.scrollTop = container.scrollHeight;
    }

    // Test if the initChatWidget function is available
    if (typeof window.initChatWidget === 'function') {
      log('initChatWidget function is available', 'success');
      
      // Initialize the widget
      try {
        window.initChatWidget({
          clientId: "test-client",
          apiKey: ";C\\}/{ap,76L'*m7U2!{K|21?n[*4i`;QuL",
          uuid: "test_user",
          apiUrl: "http://localhost:8080"
        });
        
        log('ChatWidget initialized successfully!', 'success');
      } catch (error) {
        log(`Error initializing ChatWidget: ${error.message}`, 'error');
      }
    } else {
      log('initChatWidget function is NOT available', 'error');
    }
  </script>
</body>
</html>
