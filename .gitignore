# Ignore .cpython directories
.cpython/

# Ignore .DS_Store (macOS specific)
.DS_Store

# Ignore .gql files
*.gql

# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
dist-ssr/
build/
*.local

# Environment files
.env
.env.*
!.env.example

# Editor and OS files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Debug logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Firebase
.firebase/
.firebaserc
firebase-debug.log

# Python
__pycache__/
*.py[cod]
.venv/
venv/