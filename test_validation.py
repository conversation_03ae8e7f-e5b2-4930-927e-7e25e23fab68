#!/usr/bin/env python3
"""
Unit tests for customer validation logic.
"""

import unittest
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from lib.customer_validation import (
    validate_email,
    validate_phone,
    validate_abn,
    validate_postcode,
    validate_url,
    validate_customer_data,
    sanitize_customer_data,
    ValidationError
)

class TestCustomerValidation(unittest.TestCase):
    """Test cases for customer validation functions."""

    def test_validate_email(self):
        """Test email validation."""
        # Valid emails
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        self.assertTrue(validate_email("<EMAIL>"))
        
        # Invalid emails
        self.assertFalse(validate_email(""))
        self.assertFalse(validate_email("invalid-email"))
        self.assertFalse(validate_email("@domain.com"))
        self.assertFalse(validate_email("user@"))
        self.assertFalse(validate_email("user@domain"))

    def test_validate_phone(self):
        """Test phone number validation."""
        # Valid Australian phone numbers
        self.assertTrue(validate_phone("0212345678"))  # 10 digits
        self.assertTrue(validate_phone("0412345678"))  # Mobile
        self.assertTrue(validate_phone("87654321"))    # 8 digits landline
        self.assertTrue(validate_phone("(02) 1234 5678"))  # Formatted
        self.assertTrue(validate_phone("04 1234 5678"))    # Formatted mobile
        
        # Invalid phone numbers
        self.assertFalse(validate_phone(""))
        self.assertFalse(validate_phone("123"))        # Too short
        self.assertFalse(validate_phone("*********01")) # Too long
        self.assertFalse(validate_phone("abcdefghij"))  # Non-numeric

    def test_validate_abn(self):
        """Test ABN validation."""
        # Valid ABN format (11 digits)
        self.assertTrue(validate_abn("*********01"))
        self.assertTrue(validate_abn("12 ***********"))  # Formatted
        
        # Invalid ABNs
        self.assertFalse(validate_abn(""))
        self.assertFalse(validate_abn("*********"))     # Too short
        self.assertFalse(validate_abn("*********012"))  # Too long
        self.assertFalse(validate_abn("abcdefghijk"))   # Non-numeric

    def test_validate_postcode(self):
        """Test Australian postcode validation."""
        # Valid postcodes
        self.assertTrue(validate_postcode("2000"))
        self.assertTrue(validate_postcode("3000"))
        self.assertTrue(validate_postcode("4000"))
        
        # Invalid postcodes
        self.assertFalse(validate_postcode(""))
        self.assertFalse(validate_postcode("200"))      # Too short
        self.assertFalse(validate_postcode("20000"))    # Too long
        self.assertFalse(validate_postcode("abcd"))     # Non-numeric

    def test_validate_url(self):
        """Test URL validation."""
        # Valid URLs
        self.assertTrue(validate_url("https://example.com"))
        self.assertTrue(validate_url("http://testplumbing.com.au"))
        self.assertTrue(validate_url("https://domain.co.uk/path"))
        
        # Invalid URLs
        self.assertFalse(validate_url(""))
        self.assertFalse(validate_url("not-a-url"))
        self.assertFalse(validate_url("ftp://example.com"))  # Wrong protocol
        self.assertFalse(validate_url("https://"))          # Incomplete

    def test_sanitize_customer_data(self):
        """Test data sanitization."""
        dirty_data = {
            "basic_info": {
                "company_name": "<script>alert('xss')</script>Test Company",
                "business_abn": "  *********01  ",
                "website_url": "https://example.com"
            },
            "contact_info": {
                "primary_email": "  <EMAIL>  ",
                "address": {
                    "street": "<b>123 Test St</b>",
                    "suburb": "Sydney",
                    "state": "NSW",
                    "postcode": "2000"
                }
            }
        }
        
        sanitized = sanitize_customer_data(dirty_data)
        
        # Check that HTML is escaped
        self.assertIn("&lt;script&gt;", sanitized["basic_info"]["company_name"])
        self.assertNotIn("<script>", sanitized["basic_info"]["company_name"])
        
        # Check that whitespace is trimmed
        self.assertEqual(sanitized["basic_info"]["business_abn"], "*********01")
        self.assertEqual(sanitized["contact_info"]["primary_email"], "<EMAIL>")

    def test_validate_customer_data_valid(self):
        """Test validation with valid customer data."""
        valid_data = {
            "basic_info": {
                "company_name": "Test Plumbing Services",
                "business_abn": "*********01",
                "website_url": "https://testplumbing.com.au",
                "user_type": "tradie",
                "status": "active"
            },
            "contact_info": {
                "primary_email": "<EMAIL>",
                "address": {
                    "street": "123 Test Street",
                    "suburb": "Sydney",
                    "state": "NSW",
                    "postcode": "2000"
                }
            },
            "tradie_details": {
                "tradie_type": "plumber",
                "specializations": ["Bathroom renovation", "Kitchen plumbing"],
                "service_areas": {
                    "suburbs": ["Sydney", "Parramatta"],
                    "postcodes": ["2000", "2150"],
                    "radius_km": 50
                },
                "business_hours": {
                    "monday": {"open": "08:00", "close": "17:00"},
                    "tuesday": {"open": "08:00", "close": "17:00"},
                    "wednesday": {"open": "08:00", "close": "17:00"},
                    "thursday": {"open": "08:00", "close": "17:00"},
                    "friday": {"open": "08:00", "close": "17:00"},
                    "saturday": {"open": "09:00", "close": "15:00"},
                    "sunday": {"open": "09:00", "close": "15:00"},
                    "timezone": "Australia/Sydney"
                }
            },
            "ai_settings": {
                "max_quote_amount": 5000
            }
        }
        
        result = validate_customer_data(valid_data)
        self.assertTrue(result["valid"])
        self.assertEqual(len(result["errors"]), 0)

    def test_validate_customer_data_invalid(self):
        """Test validation with invalid customer data."""
        invalid_data = {
            "basic_info": {
                "company_name": "",  # Missing required field
                "business_abn": "invalid-abn",  # Invalid format
                "website_url": "not-a-url",  # Invalid URL
                "user_type": "tradie",
                "status": "active"
            },
            "contact_info": {
                "primary_email": "invalid-email",  # Invalid email
                "address": {
                    "street": "",  # Missing required field
                    "suburb": "Sydney",
                    "state": "NSW",
                    "postcode": "invalid"  # Invalid postcode
                }
            },
            "tradie_details": {
                "tradie_type": "",  # Missing required field
                "specializations": []  # Empty array
            }
        }
        
        result = validate_customer_data(invalid_data)
        self.assertFalse(result["valid"])
        self.assertGreater(len(result["errors"]), 0)
        
        # Check specific error messages
        errors = result["errors"]
        self.assertIn("basic_info.company_name", errors)
        self.assertIn("basic_info.business_abn", errors)
        self.assertIn("basic_info.website_url", errors)
        self.assertIn("contact_info.primary_email", errors)
        self.assertIn("contact_info.address.street", errors)
        self.assertIn("contact_info.address.postcode", errors)
        self.assertIn("tradie_details.tradie_type", errors)
        self.assertIn("tradie_details.specializations", errors)

if __name__ == "__main__":
    print("Running Customer Validation Tests")
    print("=" * 40)
    
    # Run the tests
    unittest.main(verbosity=2)
