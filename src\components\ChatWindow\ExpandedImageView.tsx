import React from 'react';

interface ExpandedImageViewProps {
  imageUrl: string | null;
  onClose: () => void;
}

const ExpandedImageView: React.FC<ExpandedImageViewProps> = ({ imageUrl, onClose }) => {
  if (!imageUrl) return null;

  return (
    <div className="expanded-image-overlay" onClick={onClose} role="dialog" aria-modal="true" aria-label="Expanded image view">
      <div className="expanded-image-container" onClick={(e) => e.stopPropagation()}> {/* Prevents closing when clicking on image itself */}
        <img src={imageUrl} alt="Expanded view" />
        <button
          className="close-expanded-btn"
          onClick={onClose}
          aria-label="Close expanded image"
          title="Close expanded image"
        >
          ×
        </button>
      </div>
    </div>
  );
};

export default ExpandedImageView;